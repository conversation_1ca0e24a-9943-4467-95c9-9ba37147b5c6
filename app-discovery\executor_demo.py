"""
执行器演示脚本 - 展示不同执行器的使用方法
"""
import os
import sys
from typing import Optional

from core import ExecutorInterface, MockExecutor
from app_explorer import AppExplorer

# 尝试导入可选的执行器
try:
    from core import ADBExecutor, ADB_AVAILABLE
except ImportError:
    ADBExecutor = None
    ADB_AVAILABLE = False

try:
    from core import UIAutomator2Executor, UIAUTOMATOR2_AVAILABLE
except ImportError:
    UIAutomator2Executor = None
    UIAUTOMATOR2_AVAILABLE = False


def create_executor(executor_type: str = "mock", device_id: str = None) -> Optional[ExecutorInterface]:
    """
    创建指定类型的执行器
    
    Args:
        executor_type: 执行器类型 ("mock", "adb", "uiautomator2")
        device_id: 设备ID（可选）
    
    Returns:
        ExecutorInterface: 执行器实例
    """
    
    if executor_type.lower() == "mock":
        print("🎮 使用Mock执行器（演示模式）")
        return MockExecutor()
    
    elif executor_type.lower() == "adb":
        if not ADB_AVAILABLE:
            print("❌ ADB执行器不可用")
            return None
        
        print("📱 使用ADB执行器")
        try:
            return ADBExecutor(device_id=device_id)
        except Exception as e:
            print(f"❌ ADB执行器初始化失败: {str(e)}")
            return None
    
    elif executor_type.lower() == "uiautomator2":
        if not UIAUTOMATOR2_AVAILABLE:
            print("❌ UIAutomator2执行器不可用，请安装: pip install uiautomator2")
            return None
        
        print("🤖 使用UIAutomator2执行器")
        try:
            return UIAutomator2Executor(device_id=device_id)
        except Exception as e:
            print(f"❌ UIAutomator2执行器初始化失败: {str(e)}")
            return None
    
    else:
        print(f"❌ 不支持的执行器类型: {executor_type}")
        return None


def test_executor_basic_operations(executor: ExecutorInterface):
    """测试执行器的基本操作"""
    print("\n🧪 测试执行器基本操作")
    print("=" * 50)
    
    # 测试获取页面信息
    print("📱 获取当前页面信息...")
    page_info = executor.get_current_page_info()
    print(f"   - 截图路径: {page_info.screenshot.image_path}")
    print(f"   - 页面元素数量: {len(page_info.elements)}")
    print(f"   - 当前Activity: {page_info.activity_name}")
    print(f"   - 包名: {page_info.package_name}")
    
    # 测试自然语言操作
    test_actions = [
        "点击登录按钮",
        "输入用户名 'testuser'",
        "滚动页面向下",
        "返回上一页",
        "等待2秒"
    ]
    
    print("\n🎯 测试自然语言操作:")
    for action in test_actions:
        print(f"\n执行: {action}")
        result = executor.execute_action(action)
        
        if result.success:
            print(f"   ✅ 成功: {result.message}")
        else:
            print(f"   ❌ 失败: {result.message}")
            if result.error_details:
                print(f"   详细错误: {result.error_details}")


def run_app_exploration_demo(executor: ExecutorInterface, apk_path: str = None):
    """运行完整的应用探索演示"""
    print("\n🚀 运行应用探索演示")
    print("=" * 50)
    
    # 创建应用探索器
    explorer = AppExplorer(
        executor=executor,
        llm_model="gpt-4"  # 需要配置OpenAI API密钥
    )
    
    # 设置演示参数
    if not apk_path:
        apk_path = "/path/to/demo/app.apk"  # 替换为实际的APK路径
    
    demo_scenario = {
        "apk_path": apk_path,
        "goal": "探索应用的登录流程和主要功能页面",
        "target_features": ["用户登录", "主页导航", "设置页面"],
        "completion_criteria": ["成功登录", "访问主页", "找到设置"]
    }
    
    print(f"📱 目标APK: {demo_scenario['apk_path']}")
    print(f"🎯 探索目标: {demo_scenario['goal']}")
    
    # 运行探索
    result = explorer.explore_app(
        apk_path=demo_scenario["apk_path"],
        goal_description=demo_scenario["goal"],
        target_features=demo_scenario["target_features"],
        completion_criteria=demo_scenario["completion_criteria"],
        max_retries=3,
        generate_report=True
    )
    
    # 显示结果
    if result.get("success"):
        print("\n🎉 探索完成!")
        print(f"📊 总步数: {result.get('total_steps', 0)}")
        print(f"📱 发现页面: {result.get('pages_discovered', 0)}")
        print(f"🏁 终止原因: {result.get('termination_reason', 'N/A')}")
        
        if result.get("report_path"):
            print(f"📄 详细报告: {result['report_path']}")
    else:
        print(f"\n❌ 探索失败: {result.get('error')}")


def show_available_executors():
    """显示可用的执行器"""
    print("🔧 可用的执行器:")
    print("  - mock: Mock执行器（演示模式，无需真实设备）")
    
    if ADB_AVAILABLE:
        print("  - adb: ADB执行器（需要ADB工具和连接的Android设备）")
    else:
        print("  - adb: 不可用（需要安装ADB工具）")
    
    if UIAUTOMATOR2_AVAILABLE:
        print("  - uiautomator2: UIAutomator2执行器（需要: pip install uiautomator2）")
    else:
        print("  - uiautomator2: 不可用（需要: pip install uiautomator2）")


def main():
    """主函数"""
    print("🤖 Android应用执行器演示")
    print("=" * 60)
    
    # 显示可用执行器
    show_available_executors()
    
    # 从命令行参数获取执行器类型
    executor_type = "mock"  # 默认使用mock
    device_id = None
    apk_path = None
    
    if len(sys.argv) > 1:
        executor_type = sys.argv[1]
    if len(sys.argv) > 2:
        device_id = sys.argv[2]
    if len(sys.argv) > 3:
        apk_path = sys.argv[3]
    
    print(f"\n🎯 使用执行器类型: {executor_type}")
    if device_id:
        print(f"📱 目标设备: {device_id}")
    
    # 创建执行器
    executor = create_executor(executor_type, device_id)
    if not executor:
        print("❌ 执行器创建失败，退出程序")
        return
    
    try:
        # 测试基本操作
        test_executor_basic_operations(executor)
        
        # 如果不是Mock执行器且提供了APK路径，运行完整探索
        if executor_type != "mock" and apk_path and os.path.exists(apk_path):
            run_app_exploration_demo(executor, apk_path)
        elif executor_type == "mock":
            # Mock模式运行演示
            run_app_exploration_demo(executor)
        else:
            print("\n💡 提示: 要运行完整的应用探索，请提供有效的APK文件路径")
            print("   用法: python executor_demo.py <executor_type> [device_id] [apk_path]")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断演示")
    except Exception as e:
        print(f"\n💥 演示过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if hasattr(executor, 'close_app'):
            try:
                executor.close_app()
            except:
                pass


if __name__ == "__main__":
    main()
