"""
LangGraph工作流定义 - 编排整个应用探索流程
"""

from typing import Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from core.executor import ExecutorInterface
from .exploration_state import ExplorationState, ExplorationGoal, create_initial_state
from .exploration_nodes import ExplorationNodes
from core.page_manager import PageNodeManager


class AppExplorationWorkflow:
    """应用探索工作流"""

    def __init__(
        self,
        executor: ExecutorInterface,
        page_manager: PageNodeManager,
        llm_model: str = "gpt-4",
    ):
        self.executor = executor
        self.page_manager = page_manager
        self.nodes = ExplorationNodes(executor, page_manager, llm_model)
        self.workflow = self._build_workflow()

    def _build_workflow(self) -> StateGraph:
        """构建LangGraph工作流"""

        # 创建状态图
        workflow = StateGraph(ExplorationState)

        # 添加节点
        workflow.add_node("initialize_app", self.nodes.initialize_app)
        workflow.add_node("analyze_page", self.nodes.analyze_current_page)
        workflow.add_node("plan_action", self.nodes.plan_next_action)
        workflow.add_node("execute_action", self.nodes.execute_action)
        workflow.add_node("generate_report", self.nodes.generate_report)

        # 设置入口点
        workflow.set_entry_point("initialize_app")

        # 定义流程边
        workflow.add_edge("initialize_app", "analyze_page")
        workflow.add_edge("analyze_page", "plan_action")
        workflow.add_edge("execute_action", "analyze_page")

        # 添加条件边
        workflow.add_conditional_edges(
            "plan_action",
            self._should_continue_exploration,
            {"continue": "execute_action", "finish": "generate_report"},
        )

        workflow.add_edge("generate_report", END)

        return workflow

    def _should_continue_exploration(self, state: ExplorationState) -> str:
        """判断是否继续探索"""
        if not state["should_continue"]:
            return "finish"

        if state["retry_count"] >= state["max_retries"]:
            return "finish"

        return "continue"

    def run_exploration(
        self,
        apk_path: str,
        exploration_goal: ExplorationGoal,
        session_id: str = None,
        max_retries: int = 3,
    ) -> Dict[str, Any]:
        """运行完整的应用探索流程"""

        print(f"🎯 开始应用探索")
        print(f"📱 APK路径: {apk_path}")
        print(f"🎯 探索目标: {exploration_goal.description}")
        print("=" * 50)

        # 创建初始状态
        initial_state = create_initial_state(
            apk_path=apk_path,
            exploration_goal=exploration_goal,
            session_id=session_id,
            max_retries=max_retries,
        )

        # 编译工作流
        app = self.workflow.compile(checkpointer=MemorySaver())

        # 运行工作流
        try:
            config = {"configurable": {"thread_id": initial_state["session_id"]}}
            final_state = None

            for state in app.stream(initial_state, config):
                # 打印当前状态信息
                current_node = list(state.keys())[0]
                current_state = list(state.values())[0]

                print(f"\n📍 当前节点: {current_node}")
                if current_state.get("current_step"):
                    print(f"📊 步骤: {current_state['current_step']}")
                if current_state.get("last_error"):
                    print(f"⚠️ 错误: {current_state['last_error']}")

                final_state = current_state

            print("\n" + "=" * 50)
            print("🏁 探索完成!")

            # 返回最终结果
            if final_state:
                return {
                    "success": True,
                    "session_id": final_state["session_id"],
                    "exploration_report": final_state.get("exploration_report", {}),
                    "termination_reason": final_state.get("termination_reason"),
                    "total_steps": final_state.get("current_step", 0),
                    "pages_discovered": len(final_state.get("visited_pages", {})),
                }
            else:
                return {"success": False, "error": "工作流执行失败，未获得最终状态"}

        except Exception as e:
            error_msg = f"工作流执行异常: {str(e)}"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

    def save_exploration_state(self, session_id: str, file_path: str):
        """保存探索状态到文件"""
        try:
            # 这里需要从checkpointer获取状态
            # 由于MemorySaver的限制，这是一个简化实现
            print(f"💾 保存探索状态到: {file_path}")
            # 实际实现需要根据LangGraph的API来获取状态
        except Exception as e:
            print(f"❌ 保存状态失败: {str(e)}")

    def load_exploration_state(self, file_path: str) -> Dict[str, Any]:
        """从文件加载探索状态"""
        try:
            from exploration_state import load_state_from_file

            return load_state_from_file(file_path)
        except Exception as e:
            print(f"❌ 加载状态失败: {str(e)}")
            return {}


def create_exploration_workflow(
    executor: ExecutorInterface,
    page_manager: PageNodeManager = None,
    llm_model: str = "gpt-4",
) -> AppExplorationWorkflow:
    """创建应用探索工作流实例"""

    if page_manager is None:
        page_manager = PageNodeManager(
            collection_name="app_exploration", persist_directory="./chroma_exploration"
        )

    return AppExplorationWorkflow(executor, page_manager, llm_model)


# 便捷函数
def run_app_exploration(
    apk_path: str,
    goal_description: str,
    executor: ExecutorInterface,
    target_features: list = None,
    completion_criteria: list = None,
    session_id: str = None,
    max_retries: int = 3,
    llm_model: str = "gpt-4",
) -> Dict[str, Any]:
    """运行应用探索的便捷函数"""

    # 创建探索目标
    goal = ExplorationGoal(
        description=goal_description,
        target_features=target_features or [],
        completion_criteria=completion_criteria or [],
        priority=1,
    )

    # 创建工作流
    workflow = create_exploration_workflow(executor=executor, llm_model=llm_model)

    # 运行探索
    return workflow.run_exploration(
        apk_path=apk_path,
        exploration_goal=goal,
        session_id=session_id,
        max_retries=max_retries,
    )


# 示例使用
if __name__ == "__main__":
    from core.executor import MockExecutor

    # 创建Mock执行器
    executor = MockExecutor()

    # 运行探索
    result = run_app_exploration(
        apk_path="/path/to/app.apk",
        goal_description="探索应用的登录和主要功能，找到用户设置页面",
        executor=executor,
        target_features=["登录", "用户设置", "主页导航"],
        completion_criteria=["成功登录", "访问设置页面", "了解主要功能"],
        llm_model="gpt-4",
    )

    print("\n📊 探索结果:")
    print(f"成功: {result['success']}")
    if result["success"]:
        print(f"会话ID: {result['session_id']}")
        print(f"总步数: {result['total_steps']}")
        print(f"发现页面: {result['pages_discovered']}")
        print(f"终止原因: {result['termination_reason']}")
    else:
        print(f"错误: {result['error']}")
