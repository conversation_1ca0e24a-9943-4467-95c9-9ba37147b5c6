"""
基于ADB和UIAutomator的Android应用执行器实现
"""
import subprocess
import json
import time
import base64
import os
import re
from typing import Dict, Any, Optional, List
from datetime import datetime

from .executor import ExecutorInterface, ActionResult, PageInfo, ScreenshotResult, UIElement


class ADBExecutor(ExecutorInterface):
    """基于ADB的Android应用执行器"""
    
    def __init__(self, device_id: str = None, adb_path: str = "adb"):
        """
        初始化ADB执行器
        
        Args:
            device_id: Android设备ID，如果为None则使用默认设备
            adb_path: ADB可执行文件路径
        """
        self.device_id = device_id
        self.adb_path = adb_path
        self.current_package = None
        self.screenshots_dir = "./screenshots"
        
        # 确保截图目录存在
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # 验证ADB连接
        self._verify_adb_connection()
    
    def _verify_adb_connection(self):
        """验证ADB连接"""
        try:
            result = self._run_adb_command(["devices"])
            if "device" not in result:
                raise Exception("没有找到连接的Android设备")
        except Exception as e:
            raise Exception(f"ADB连接失败: {str(e)}")
    
    def _run_adb_command(self, command: List[str], timeout: int = 30) -> str:
        """执行ADB命令"""
        cmd = [self.adb_path]
        if self.device_id:
            cmd.extend(["-s", self.device_id])
        cmd.extend(command)
        
        try:
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=timeout,
                encoding='utf-8'
            )
            
            if result.returncode != 0:
                raise Exception(f"ADB命令执行失败: {result.stderr}")
            
            return result.stdout
        except subprocess.TimeoutExpired:
            raise Exception(f"ADB命令执行超时: {' '.join(cmd)}")
        except Exception as e:
            raise Exception(f"ADB命令执行异常: {str(e)}")
    
    def install_and_launch_app(self, apk_path: str) -> ActionResult:
        """安装并启动应用"""
        try:
            # 安装APK
            print(f"📱 正在安装APK: {apk_path}")
            install_result = self._run_adb_command(["install", "-r", apk_path])
            
            if "Success" not in install_result:
                return ActionResult(
                    success=False,
                    message=f"APK安装失败: {install_result}",
                    error_details=install_result
                )
            
            # 获取包名
            package_name = self._get_package_name(apk_path)
            if not package_name:
                return ActionResult(
                    success=False,
                    message="无法获取应用包名"
                )
            
            self.current_package = package_name
            
            # 启动应用
            print(f"🚀 正在启动应用: {package_name}")
            launch_result = self._run_adb_command([
                "shell", "monkey", "-p", package_name, "-c", 
                "android.intent.category.LAUNCHER", "1"
            ])
            
            # 等待应用启动
            time.sleep(3)
            
            # 获取当前页面信息
            page_info = self.get_current_page_info()
            
            return ActionResult(
                success=True,
                message=f"成功安装并启动应用: {package_name}",
                new_page_info=page_info
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                message=f"安装启动应用失败: {str(e)}",
                error_details=str(e)
            )
    
    def _get_package_name(self, apk_path: str) -> Optional[str]:
        """从APK文件获取包名"""
        try:
            # 使用aapt获取包名
            result = subprocess.run(
                ["aapt", "dump", "badging", apk_path],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if line.startswith('package:'):
                        match = re.search(r"name='([^']+)'", line)
                        if match:
                            return match.group(1)
            
            # 如果aapt不可用，尝试其他方法
            return None
            
        except Exception:
            return None
    
    def get_current_page_info(self) -> PageInfo:
        """获取当前页面信息"""
        try:
            # 获取截图
            screenshot = self._take_screenshot()
            
            # 获取UI层次结构
            elements = self._get_ui_elements()
            
            # 获取当前Activity信息
            activity_info = self._get_current_activity()
            
            return PageInfo(
                screenshot=screenshot,
                elements=elements,
                activity_name=activity_info.get("activity"),
                package_name=activity_info.get("package"),
                page_source=None  # 可以添加XML dump
            )
            
        except Exception as e:
            # 返回基本的页面信息
            return PageInfo(
                screenshot=ScreenshotResult(
                    image_path="",
                    image_base64="",
                    width=0,
                    height=0,
                    timestamp=datetime.now().isoformat()
                ),
                elements=[],
                activity_name=None,
                package_name=self.current_package
            )
    
    def _take_screenshot(self) -> ScreenshotResult:
        """截取屏幕截图"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        remote_path = f"/sdcard/screenshot_{timestamp}.png"
        local_path = os.path.join(self.screenshots_dir, f"screenshot_{timestamp}.png")
        
        try:
            # 在设备上截图
            self._run_adb_command(["shell", "screencap", "-p", remote_path])
            
            # 拉取到本地
            self._run_adb_command(["pull", remote_path, local_path])
            
            # 删除设备上的临时文件
            self._run_adb_command(["shell", "rm", remote_path])
            
            # 获取图片尺寸
            width, height = self._get_image_size(local_path)
            
            # 转换为base64
            with open(local_path, "rb") as f:
                image_base64 = base64.b64encode(f.read()).decode()
            
            return ScreenshotResult(
                image_path=local_path,
                image_base64=image_base64,
                width=width,
                height=height,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            print(f"截图失败: {str(e)}")
            return ScreenshotResult(
                image_path="",
                image_base64="",
                width=0,
                height=0,
                timestamp=datetime.now().isoformat()
            )
    
    def _get_image_size(self, image_path: str) -> tuple:
        """获取图片尺寸"""
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                return img.size
        except:
            # 如果PIL不可用，返回默认尺寸
            return (1080, 1920)
    
    def _get_ui_elements(self) -> List[UIElement]:
        """获取UI元素信息"""
        try:
            # 使用uiautomator dump获取UI层次结构
            self._run_adb_command(["shell", "uiautomator", "dump", "/sdcard/ui_dump.xml"])
            
            # 拉取XML文件
            xml_content = self._run_adb_command(["shell", "cat", "/sdcard/ui_dump.xml"])
            
            # 解析XML获取UI元素
            elements = self._parse_ui_xml(xml_content)
            
            # 清理临时文件
            self._run_adb_command(["shell", "rm", "/sdcard/ui_dump.xml"])
            
            return elements
            
        except Exception as e:
            print(f"获取UI元素失败: {str(e)}")
            return []
    
    def _parse_ui_xml(self, xml_content: str) -> List[UIElement]:
        """解析UI XML内容"""
        elements = []
        try:
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_content)
            
            for node in root.iter():
                if node.tag == "node":
                    bounds_str = node.get("bounds", "")
                    bounds = self._parse_bounds(bounds_str)
                    
                    element = UIElement(
                        type=node.get("class", "").split(".")[-1].lower(),
                        text=node.get("text"),
                        hint=node.get("content-desc"),
                        bounds=bounds,
                        clickable=node.get("clickable") == "true",
                        scrollable=node.get("scrollable") == "true",
                        attributes={
                            "resource-id": node.get("resource-id"),
                            "package": node.get("package"),
                            "enabled": node.get("enabled"),
                            "focused": node.get("focused"),
                            "selected": node.get("selected")
                        }
                    )
                    elements.append(element)
            
        except Exception as e:
            print(f"解析UI XML失败: {str(e)}")
        
        return elements
    
    def _parse_bounds(self, bounds_str: str) -> Optional[List[int]]:
        """解析bounds字符串，格式: [x1,y1][x2,y2]"""
        try:
            import re
            matches = re.findall(r'\[(\d+),(\d+)\]', bounds_str)
            if len(matches) == 2:
                x1, y1 = map(int, matches[0])
                x2, y2 = map(int, matches[1])
                return [x1, y1, x2, y2]
        except:
            pass
        return None
    
    def _get_current_activity(self) -> Dict[str, str]:
        """获取当前Activity信息"""
        try:
            result = self._run_adb_command([
                "shell", "dumpsys", "window", "windows", "|", "grep", "-E", "mCurrentFocus"
            ])
            
            # 解析输出获取包名和Activity名
            if "mCurrentFocus" in result:
                match = re.search(r'mCurrentFocus=Window\{[^}]+ u0 ([^/]+)/([^}]+)\}', result)
                if match:
                    return {
                        "package": match.group(1),
                        "activity": match.group(2)
                    }
            
            return {"package": self.current_package, "activity": None}
            
        except Exception:
            return {"package": self.current_package, "activity": None}

    def execute_action(self, action_description: str) -> ActionResult:
        """根据自然语言描述执行操作"""
        try:
            print(f"🎯 执行操作: {action_description}")

            # 获取当前页面信息
            current_page = self.get_current_page_info()

            # 解析操作类型和目标
            action_type, target_info = self._parse_action_description(action_description, current_page.elements)

            if action_type == "click":
                return self._execute_click_action(target_info, current_page)
            elif action_type == "input":
                return self._execute_input_action(target_info, current_page)
            elif action_type == "scroll":
                return self._execute_scroll_action(target_info)
            elif action_type == "back":
                return self.go_back()
            elif action_type == "wait":
                return self._execute_wait_action(target_info)
            else:
                return ActionResult(
                    success=False,
                    message=f"不支持的操作类型: {action_description}",
                    error_details=f"无法解析操作: {action_description}"
                )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"执行操作失败: {str(e)}",
                error_details=str(e)
            )

    def _parse_action_description(self, description: str, elements: List[UIElement]) -> tuple:
        """解析自然语言操作描述"""
        description_lower = description.lower()

        # 点击操作
        if any(keyword in description_lower for keyword in ["点击", "click", "tap", "按"]):
            target_element = self._find_target_element(description, elements)
            return "click", target_element

        # 输入操作
        elif any(keyword in description_lower for keyword in ["输入", "input", "type", "填写"]):
            input_info = self._parse_input_action(description, elements)
            return "input", input_info

        # 滚动操作
        elif any(keyword in description_lower for keyword in ["滚动", "scroll", "滑动", "swipe"]):
            scroll_info = self._parse_scroll_action(description)
            return "scroll", scroll_info

        # 返回操作
        elif any(keyword in description_lower for keyword in ["返回", "back", "后退"]):
            return "back", None

        # 等待操作
        elif any(keyword in description_lower for keyword in ["等待", "wait", "sleep"]):
            wait_time = self._parse_wait_time(description)
            return "wait", wait_time

        else:
            # 默认尝试查找可点击元素
            target_element = self._find_target_element(description, elements)
            return "click", target_element

    def _find_target_element(self, description: str, elements: List[UIElement]) -> Optional[UIElement]:
        """根据描述查找目标UI元素"""
        description_lower = description.lower()

        # 提取关键词
        keywords = []
        for word in description_lower.split():
            if len(word) > 1 and word not in ["点击", "click", "按钮", "button"]:
                keywords.append(word)

        best_match = None
        best_score = 0

        for element in elements:
            if not element.clickable:
                continue

            score = 0
            element_text = (element.text or "").lower()
            element_hint = (element.hint or "").lower()
            element_type = element.type.lower()

            # 文本匹配
            for keyword in keywords:
                if keyword in element_text:
                    score += 3
                elif keyword in element_hint:
                    score += 2
                elif keyword in element_type:
                    score += 1

            # 类型匹配
            if "按钮" in description_lower and "button" in element_type:
                score += 2
            elif "输入" in description_lower and "edit" in element_type:
                score += 2

            if score > best_score:
                best_score = score
                best_match = element

        return best_match

    def _parse_input_action(self, description: str, elements: List[UIElement]) -> Dict[str, Any]:
        """解析输入操作"""
        # 提取要输入的文本
        import re

        # 查找引号中的文本
        text_match = re.search(r'["\']([^"\']+)["\']', description)
        if text_match:
            input_text = text_match.group(1)
        else:
            # 查找"输入"后面的文本
            input_match = re.search(r'输入\s*(.+)', description)
            if input_match:
                input_text = input_match.group(1).strip()
            else:
                input_text = ""

        # 查找输入框
        target_element = None
        for element in elements:
            if element.type in ["edittext", "textfield"] or "edit" in element.type.lower():
                target_element = element
                break

        return {
            "text": input_text,
            "element": target_element
        }

    def _parse_scroll_action(self, description: str) -> Dict[str, Any]:
        """解析滚动操作"""
        description_lower = description.lower()

        if any(word in description_lower for word in ["向上", "up"]):
            direction = "up"
        elif any(word in description_lower for word in ["向下", "down"]):
            direction = "down"
        elif any(word in description_lower for word in ["向左", "left"]):
            direction = "left"
        elif any(word in description_lower for word in ["向右", "right"]):
            direction = "right"
        else:
            direction = "down"  # 默认向下滚动

        return {"direction": direction, "distance": 500}

    def _parse_wait_time(self, description: str) -> float:
        """解析等待时间"""
        import re

        # 查找数字
        time_match = re.search(r'(\d+(?:\.\d+)?)', description)
        if time_match:
            return float(time_match.group(1))
        else:
            return 2.0  # 默认等待2秒

    def _execute_click_action(self, target_element: Optional[UIElement], current_page: PageInfo) -> ActionResult:
        """执行点击操作"""
        if not target_element:
            return ActionResult(
                success=False,
                message="未找到可点击的目标元素"
            )

        if not target_element.bounds:
            return ActionResult(
                success=False,
                message="目标元素没有有效的坐标信息"
            )

        # 计算点击坐标（元素中心点）
        x1, y1, x2, y2 = target_element.bounds
        click_x = (x1 + x2) // 2
        click_y = (y1 + y2) // 2

        return self.click_element(click_x, click_y)

    def _execute_input_action(self, input_info: Dict[str, Any], current_page: PageInfo) -> ActionResult:
        """执行输入操作"""
        text = input_info.get("text", "")
        element = input_info.get("element")

        if not text:
            return ActionResult(
                success=False,
                message="没有指定要输入的文本"
            )

        if element and element.bounds:
            # 先点击输入框
            x1, y1, x2, y2 = element.bounds
            click_x = (x1 + x2) // 2
            click_y = (y1 + y2) // 2

            click_result = self.click_element(click_x, click_y)
            if not click_result.success:
                return click_result

            time.sleep(0.5)  # 等待输入框获得焦点

        return self.input_text(text, element.bounds if element else None)

    def _execute_scroll_action(self, scroll_info: Dict[str, Any]) -> ActionResult:
        """执行滚动操作"""
        direction = scroll_info.get("direction", "down")
        distance = scroll_info.get("distance", 500)

        return self.scroll(direction, distance)

    def _execute_wait_action(self, wait_time: float) -> ActionResult:
        """执行等待操作"""
        try:
            time.sleep(wait_time)
            return ActionResult(
                success=True,
                message=f"等待了 {wait_time} 秒"
            )
        except Exception as e:
            return ActionResult(
                success=False,
                message=f"等待操作失败: {str(e)}"
            )

    def click_element(self, x: int, y: int) -> ActionResult:
        """点击指定坐标"""
        try:
            print(f"👆 点击坐标: ({x}, {y})")
            self._run_adb_command(["shell", "input", "tap", str(x), str(y)])

            time.sleep(1)  # 等待界面响应

            # 获取新的页面信息
            new_page_info = self.get_current_page_info()

            return ActionResult(
                success=True,
                message=f"成功点击坐标 ({x}, {y})",
                new_page_info=new_page_info
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"点击操作失败: {str(e)}",
                error_details=str(e)
            )

    def input_text(self, text: str, element_bounds: Optional[List[int]] = None) -> ActionResult:
        """输入文本"""
        try:
            print(f"⌨️ 输入文本: {text}")

            # 清除现有文本
            self._run_adb_command(["shell", "input", "keyevent", "KEYCODE_CTRL_A"])
            time.sleep(0.2)
            self._run_adb_command(["shell", "input", "keyevent", "KEYCODE_DEL"])
            time.sleep(0.2)

            # 输入新文本
            escaped_text = text.replace(" ", "%s").replace("'", "\\'")
            self._run_adb_command(["shell", "input", "text", escaped_text])

            time.sleep(1)  # 等待输入完成

            # 获取新的页面信息
            new_page_info = self.get_current_page_info()

            return ActionResult(
                success=True,
                message=f"成功输入文本: {text}",
                new_page_info=new_page_info
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"输入文本失败: {str(e)}",
                error_details=str(e)
            )

    def scroll(self, direction: str = "down", distance: int = 500) -> ActionResult:
        """滚动页面"""
        try:
            print(f"📜 滚动: {direction}, 距离: {distance}")

            # 获取屏幕尺寸
            screen_size = self._get_screen_size()
            center_x = screen_size[0] // 2
            center_y = screen_size[1] // 2

            # 计算滚动起点和终点
            if direction == "down":
                start_x, start_y = center_x, center_y + distance // 2
                end_x, end_y = center_x, center_y - distance // 2
            elif direction == "up":
                start_x, start_y = center_x, center_y - distance // 2
                end_x, end_y = center_x, center_y + distance // 2
            elif direction == "left":
                start_x, start_y = center_x + distance // 2, center_y
                end_x, end_y = center_x - distance // 2, center_y
            elif direction == "right":
                start_x, start_y = center_x - distance // 2, center_y
                end_x, end_y = center_x + distance // 2, center_y
            else:
                return ActionResult(
                    success=False,
                    message=f"不支持的滚动方向: {direction}"
                )

            # 执行滑动
            self._run_adb_command([
                "shell", "input", "swipe",
                str(start_x), str(start_y), str(end_x), str(end_y), "300"
            ])

            time.sleep(1)  # 等待滚动完成

            # 获取新的页面信息
            new_page_info = self.get_current_page_info()

            return ActionResult(
                success=True,
                message=f"成功向{direction}滚动{distance}像素",
                new_page_info=new_page_info
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"滚动操作失败: {str(e)}",
                error_details=str(e)
            )

    def _get_screen_size(self) -> tuple:
        """获取屏幕尺寸"""
        try:
            result = self._run_adb_command(["shell", "wm", "size"])
            # 输出格式: Physical size: 1080x1920
            import re
            match = re.search(r'(\d+)x(\d+)', result)
            if match:
                return (int(match.group(1)), int(match.group(2)))
            else:
                return (1080, 1920)  # 默认尺寸
        except:
            return (1080, 1920)  # 默认尺寸

    def go_back(self) -> ActionResult:
        """返回上一页"""
        try:
            print("⬅️ 返回上一页")
            self._run_adb_command(["shell", "input", "keyevent", "KEYCODE_BACK"])

            time.sleep(1)  # 等待页面切换

            # 获取新的页面信息
            new_page_info = self.get_current_page_info()

            return ActionResult(
                success=True,
                message="成功返回上一页",
                new_page_info=new_page_info
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"返回操作失败: {str(e)}",
                error_details=str(e)
            )

    def close_app(self) -> ActionResult:
        """关闭应用"""
        try:
            if not self.current_package:
                return ActionResult(
                    success=False,
                    message="没有正在运行的应用"
                )

            print(f"🔴 关闭应用: {self.current_package}")
            self._run_adb_command(["shell", "am", "force-stop", self.current_package])

            return ActionResult(
                success=True,
                message=f"成功关闭应用: {self.current_package}"
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"关闭应用失败: {str(e)}",
                error_details=str(e)
            )

    def is_app_running(self) -> bool:
        """检查应用是否正在运行"""
        try:
            if not self.current_package:
                return False

            result = self._run_adb_command([
                "shell", "ps", "|", "grep", self.current_package
            ])

            return self.current_package in result

        except Exception:
            return False

    def get_device_info(self) -> Dict[str, str]:
        """获取设备信息"""
        try:
            info = {}

            # 设备型号
            info["model"] = self._run_adb_command([
                "shell", "getprop", "ro.product.model"
            ]).strip()

            # Android版本
            info["android_version"] = self._run_adb_command([
                "shell", "getprop", "ro.build.version.release"
            ]).strip()

            # API级别
            info["api_level"] = self._run_adb_command([
                "shell", "getprop", "ro.build.version.sdk"
            ]).strip()

            # 屏幕密度
            info["density"] = self._run_adb_command([
                "shell", "getprop", "ro.sf.lcd_density"
            ]).strip()

            return info

        except Exception as e:
            return {"error": str(e)}

    def wait_for_element(self, element_description: str, timeout: int = 10) -> Optional[UIElement]:
        """等待元素出现"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                page_info = self.get_current_page_info()
                target_element = self._find_target_element(element_description, page_info.elements)

                if target_element:
                    return target_element

                time.sleep(1)

            except Exception:
                time.sleep(1)

        return None
