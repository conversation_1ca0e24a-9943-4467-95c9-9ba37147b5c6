"""
Executor接口定义 - 负责与应用进行实际交互
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import base64


@dataclass
class ScreenshotResult:
    """截图结果"""
    image_path: str
    image_base64: str
    width: int
    height: int
    timestamp: str


@dataclass
class UIElement:
    """UI元素信息"""
    type: str  # button, input, text, image等
    text: Optional[str] = None
    hint: Optional[str] = None
    bounds: Optional[List[int]] = None  # [x1, y1, x2, y2]
    clickable: bool = False
    scrollable: bool = False
    attributes: Optional[Dict[str, Any]] = None


@dataclass
class PageInfo:
    """页面信息"""
    screenshot: ScreenshotResult
    elements: List[UIElement]
    activity_name: Optional[str] = None
    package_name: Optional[str] = None
    page_source: Optional[str] = None


@dataclass
class ActionResult:
    """操作执行结果"""
    success: bool
    message: str
    new_page_info: Optional[PageInfo] = None
    error_details: Optional[str] = None


class ExecutorInterface(ABC):
    """Executor接口 - 定义与应用交互的标准接口"""
    
    @abstractmethod
    def install_and_launch_app(self, apk_path: str) -> ActionResult:
        """
        安装并启动应用
        
        Args:
            apk_path: APK文件路径
            
        Returns:
            ActionResult: 操作结果
        """
        pass
    
    @abstractmethod
    def get_current_page_info(self) -> PageInfo:
        """
        获取当前页面信息，包括截图和UI元素
        
        Returns:
            PageInfo: 当前页面信息
        """
        pass
    
    @abstractmethod
    def execute_action(self, action_description: str) -> ActionResult:
        """
        根据自然语言描述执行操作
        
        Args:
            action_description: 自然语言描述的操作，如"点击登录按钮"、"输入用户名123"
            
        Returns:
            ActionResult: 操作执行结果
        """
        pass
    
    @abstractmethod
    def click_element(self, x: int, y: int) -> ActionResult:
        """
        点击指定坐标
        
        Args:
            x, y: 点击坐标
            
        Returns:
            ActionResult: 操作结果
        """
        pass
    
    @abstractmethod
    def input_text(self, text: str, element_bounds: Optional[List[int]] = None) -> ActionResult:
        """
        输入文本
        
        Args:
            text: 要输入的文本
            element_bounds: 目标输入框的边界坐标 [x1, y1, x2, y2]
            
        Returns:
            ActionResult: 操作结果
        """
        pass
    
    @abstractmethod
    def scroll(self, direction: str = "down", distance: int = 500) -> ActionResult:
        """
        滚动页面
        
        Args:
            direction: 滚动方向 "up", "down", "left", "right"
            distance: 滚动距离（像素）
            
        Returns:
            ActionResult: 操作结果
        """
        pass
    
    @abstractmethod
    def go_back(self) -> ActionResult:
        """
        返回上一页
        
        Returns:
            ActionResult: 操作结果
        """
        pass
    
    @abstractmethod
    def close_app(self) -> ActionResult:
        """
        关闭应用
        
        Returns:
            ActionResult: 操作结果
        """
        pass
    
    @abstractmethod
    def is_app_running(self) -> bool:
        """
        检查应用是否正在运行
        
        Returns:
            bool: 应用是否在运行
        """
        pass


class MockExecutor(ExecutorInterface):
    """
    Mock Executor实现 - 用于测试和演示
    """
    
    def __init__(self):
        self.current_page = "splash"
        self.app_running = False
        self.page_history = []
    
    def install_and_launch_app(self, apk_path: str) -> ActionResult:
        """模拟安装并启动应用"""
        self.app_running = True
        self.current_page = "splash"
        
        # 模拟启动页面信息
        page_info = PageInfo(
            screenshot=ScreenshotResult(
                image_path="mock_splash.png",
                image_base64="mock_base64_data",
                width=1080,
                height=1920,
                timestamp="2024-01-01T00:00:00"
            ),
            elements=[
                UIElement(type="image", text="应用Logo", bounds=[400, 500, 680, 780]),
                UIElement(type="text", text="欢迎使用", bounds=[300, 800, 780, 850])
            ],
            activity_name="com.example.SplashActivity",
            package_name="com.example.app"
        )
        
        return ActionResult(
            success=True,
            message=f"成功安装并启动应用: {apk_path}",
            new_page_info=page_info
        )
    
    def get_current_page_info(self) -> PageInfo:
        """获取当前页面信息"""
        # 根据当前页面返回不同的模拟数据
        if self.current_page == "splash":
            return PageInfo(
                screenshot=ScreenshotResult(
                    image_path="mock_splash.png",
                    image_base64="mock_splash_base64",
                    width=1080,
                    height=1920,
                    timestamp="2024-01-01T00:00:00"
                ),
                elements=[
                    UIElement(type="image", text="应用Logo", bounds=[400, 500, 680, 780]),
                    UIElement(type="button", text="开始使用", bounds=[300, 1200, 780, 1300], clickable=True)
                ]
            )
        elif self.current_page == "login":
            return PageInfo(
                screenshot=ScreenshotResult(
                    image_path="mock_login.png",
                    image_base64="mock_login_base64",
                    width=1080,
                    height=1920,
                    timestamp="2024-01-01T00:01:00"
                ),
                elements=[
                    UIElement(type="input", hint="请输入用户名", bounds=[100, 600, 980, 680], clickable=True),
                    UIElement(type="input", hint="请输入密码", bounds=[100, 720, 980, 800], clickable=True),
                    UIElement(type="button", text="登录", bounds=[100, 900, 980, 1000], clickable=True),
                    UIElement(type="button", text="注册", bounds=[100, 1020, 980, 1120], clickable=True)
                ]
            )
        elif self.current_page == "home":
            return PageInfo(
                screenshot=ScreenshotResult(
                    image_path="mock_home.png",
                    image_base64="mock_home_base64",
                    width=1080,
                    height=1920,
                    timestamp="2024-01-01T00:02:00"
                ),
                elements=[
                    UIElement(type="text", text="首页", bounds=[50, 100, 200, 150]),
                    UIElement(type="button", text="设置", bounds=[800, 100, 950, 200], clickable=True),
                    UIElement(type="button", text="个人中心", bounds=[100, 300, 400, 400], clickable=True),
                    UIElement(type="button", text="商品列表", bounds=[500, 300, 800, 400], clickable=True)
                ]
            )
        else:
            # 默认页面
            return PageInfo(
                screenshot=ScreenshotResult(
                    image_path="mock_default.png",
                    image_base64="mock_default_base64",
                    width=1080,
                    height=1920,
                    timestamp="2024-01-01T00:03:00"
                ),
                elements=[
                    UIElement(type="text", text="未知页面", bounds=[100, 100, 500, 200])
                ]
            )
    
    def execute_action(self, action_description: str) -> ActionResult:
        """根据描述执行操作"""
        action_lower = action_description.lower()
        
        if "开始使用" in action_description or "start" in action_lower:
            self.current_page = "login"
            return ActionResult(
                success=True,
                message="点击了开始使用按钮",
                new_page_info=self.get_current_page_info()
            )
        elif "登录" in action_description or "login" in action_lower:
            self.current_page = "home"
            return ActionResult(
                success=True,
                message="执行了登录操作",
                new_page_info=self.get_current_page_info()
            )
        elif "设置" in action_description or "setting" in action_lower:
            self.current_page = "settings"
            return ActionResult(
                success=True,
                message="进入了设置页面",
                new_page_info=self.get_current_page_info()
            )
        else:
            return ActionResult(
                success=False,
                message=f"无法识别的操作: {action_description}",
                error_details="Mock executor无法处理此操作"
            )
    
    def click_element(self, x: int, y: int) -> ActionResult:
        """点击坐标"""
        return ActionResult(
            success=True,
            message=f"点击了坐标 ({x}, {y})",
            new_page_info=self.get_current_page_info()
        )
    
    def input_text(self, text: str, element_bounds: Optional[List[int]] = None) -> ActionResult:
        """输入文本"""
        return ActionResult(
            success=True,
            message=f"输入了文本: {text}",
            new_page_info=self.get_current_page_info()
        )
    
    def scroll(self, direction: str = "down", distance: int = 500) -> ActionResult:
        """滚动"""
        return ActionResult(
            success=True,
            message=f"向{direction}滚动了{distance}像素",
            new_page_info=self.get_current_page_info()
        )
    
    def go_back(self) -> ActionResult:
        """返回"""
        if self.current_page == "home":
            self.current_page = "login"
        elif self.current_page == "login":
            self.current_page = "splash"
        
        return ActionResult(
            success=True,
            message="返回上一页",
            new_page_info=self.get_current_page_info()
        )
    
    def close_app(self) -> ActionResult:
        """关闭应用"""
        self.app_running = False
        return ActionResult(
            success=True,
            message="应用已关闭"
        )
    
    def is_app_running(self) -> bool:
        """检查应用状态"""
        return self.app_running
