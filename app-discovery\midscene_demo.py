"""
Midscene执行器演示脚本
展示如何使用基于Midscene Android SDK的执行器
"""
import os
import sys
import time
from typing import Optional

from app_explorer import AppExplorer

# 尝试导入Midscene执行器
try:
    from core.midscene_executor import MidsceneExecutor
    MIDSCENE_AVAILABLE = True
except ImportError:
    MidsceneExecutor = None
    MIDSCENE_AVAILABLE = False

try:
    from core.midscene_api_executor import MidsceneAPIExecutor
    MIDSCENE_API_AVAILABLE = True
except ImportError:
    MidsceneAPIExecutor = None
    MIDSCENE_API_AVAILABLE = False


def create_midscene_executor(executor_type: str = "api", device_id: str = None):
    """
    创建Midscene执行器
    
    Args:
        executor_type: 执行器类型 ("subprocess", "api")
        device_id: 设备ID（可选）
    
    Returns:
        ExecutorInterface: 执行器实例
    """
    
    if executor_type.lower() == "subprocess":
        if not MIDSCENE_AVAILABLE:
            print("❌ Midscene子进程执行器不可用")
            return None
        
        print("🔧 使用Midscene子进程执行器")
        try:
            return MidsceneExecutor(device_id=device_id)
        except Exception as e:
            print(f"❌ Midscene子进程执行器初始化失败: {str(e)}")
            return None
    
    elif executor_type.lower() == "api":
        if not MIDSCENE_API_AVAILABLE:
            print("❌ Midscene API执行器不可用")
            return None
        
        print("🌐 使用Midscene API执行器")
        try:
            return MidsceneAPIExecutor(device_id=device_id)
        except Exception as e:
            print(f"❌ Midscene API执行器初始化失败: {str(e)}")
            return None
    
    else:
        print(f"❌ 不支持的执行器类型: {executor_type}")
        return None


def test_midscene_features(executor):
    """测试Midscene特有功能"""
    print("\n🤖 测试Midscene AI功能")
    print("=" * 50)
    
    # 测试AI查询
    if hasattr(executor, 'ai_query'):
        print("🔍 测试AI查询...")
        result = executor.ai_query("找到页面上所有的按钮和它们的文本")
        print(f"   查询结果: {result}")
    
    # 测试AI等待
    if hasattr(executor, 'ai_wait_for'):
        print("\n⏳ 测试AI等待...")
        success = executor.ai_wait_for("页面加载完成", timeout=5)
        print(f"   等待结果: {'成功' if success else '失败'}")
    
    # 测试AI断言
    if hasattr(executor, 'ai_assert'):
        print("\n✅ 测试AI断言...")
        try:
            success = executor.ai_assert("页面上有可点击的元素")
            print(f"   断言结果: {'通过' if success else '失败'}")
        except Exception as e:
            print(f"   断言异常: {str(e)}")


def run_ebay_demo(executor):
    """运行eBay演示（类似原始TypeScript示例）"""
    print("\n🛒 运行eBay搜索演示")
    print("=" * 50)
    
    try:
        # 启动eBay网站
        print("📱 启动eBay...")
        result = executor.execute_action("打开eBay网站")
        if not result.success:
            print(f"❌ 启动eBay失败: {result.message}")
            return
        
        # 等待页面加载
        print("⏳ 等待页面加载...")
        time.sleep(5)
        
        # 搜索耳机
        print("🔍 搜索耳机...")
        result = executor.execute_action('在搜索框中输入 "Headphones" 并按回车')
        if result.success:
            print("✅ 搜索操作成功")
        else:
            print(f"❌ 搜索失败: {result.message}")
            return
        
        # 等待搜索结果
        if hasattr(executor, 'ai_wait_for'):
            print("⏳ 等待搜索结果...")
            success = executor.ai_wait_for("页面上至少有一个耳机商品", timeout=10)
            if success:
                print("✅ 搜索结果已加载")
            else:
                print("⚠️ 搜索结果加载超时")
        else:
            time.sleep(5)
        
        # 使用AI查询获取商品信息
        if hasattr(executor, 'ai_query'):
            print("📊 获取商品信息...")
            items = executor.ai_query(
                "找到商品列表中的商品，返回格式：{itemTitle: string, price: number}[]"
            )
            
            if items:
                print("🎧 找到的耳机商品:")
                for i, item in enumerate(items[:3]):  # 只显示前3个
                    print(f"   {i+1}. {item.get('itemTitle', 'N/A')} - ${item.get('price', 'N/A')}")
            else:
                print("❌ 未找到商品信息")
        
        # 价格判断
        if hasattr(executor, 'ai_query'):
            print("\n💰 价格分析...")
            
            # 获取第一个商品价格
            first_price = executor.ai_query("第一个耳机商品的价格是多少？")
            print(f"   第一个商品价格: {first_price}")
            
            # 判断是否超过1000
            if hasattr(executor, 'ai_query'):
                is_expensive = executor.ai_query("第一个耳机的价格是否超过1000？")
                print(f"   价格是否超过1000: {is_expensive}")
        
        # 测试断言
        if hasattr(executor, 'ai_assert'):
            print("\n✅ 测试页面断言...")
            try:
                executor.ai_assert("页面左侧有分类筛选器")
                print("   ✅ 断言通过：页面有分类筛选器")
            except Exception as e:
                print(f"   ❌ 断言失败: {str(e)}")
        
        print("\n🎉 eBay演示完成!")
        
    except Exception as e:
        print(f"❌ eBay演示过程中发生异常: {str(e)}")


def run_app_exploration_with_midscene(executor, apk_path: str = None):
    """使用Midscene执行器运行应用探索"""
    print("\n🚀 使用Midscene执行器运行应用探索")
    print("=" * 50)
    
    # 创建应用探索器
    explorer = AppExplorer(
        executor=executor,
        llm_model="gpt-4"  # 需要配置OpenAI API密钥
    )
    
    # 设置演示参数
    if not apk_path:
        # 使用网站URL作为演示
        apk_path = "https://www.ebay.com"
    
    demo_scenario = {
        "apk_path": apk_path,
        "goal": "探索eBay网站的搜索功能，找到耳机商品并分析价格",
        "target_features": ["搜索功能", "商品列表", "价格信息", "筛选器"],
        "completion_criteria": ["成功搜索商品", "获取商品信息", "分析价格范围"]
    }
    
    print(f"🎯 目标: {demo_scenario['goal']}")
    
    # 运行探索
    result = explorer.explore_app(
        apk_path=demo_scenario["apk_path"],
        goal_description=demo_scenario["goal"],
        target_features=demo_scenario["target_features"],
        completion_criteria=demo_scenario["completion_criteria"],
        max_retries=3,
        generate_report=True
    )
    
    # 显示结果
    if result.get("success"):
        print("\n🎉 探索完成!")
        print(f"📊 总步数: {result.get('total_steps', 0)}")
        print(f"📱 发现页面: {result.get('pages_discovered', 0)}")
        print(f"🏁 终止原因: {result.get('termination_reason', 'N/A')}")
        
        if result.get("report_path"):
            print(f"📄 详细报告: {result['report_path']}")
    else:
        print(f"\n❌ 探索失败: {result.get('error')}")


def show_midscene_info():
    """显示Midscene相关信息"""
    print("🤖 Midscene Android SDK执行器")
    print("=" * 60)
    print("Midscene是一个AI驱动的Android自动化测试框架")
    print("特点:")
    print("  - 🧠 AI驱动的自然语言操作")
    print("  - 🔍 智能元素识别和查询")
    print("  - ⏳ 智能等待条件")
    print("  - ✅ AI断言验证")
    print("  - 📱 支持真实设备和模拟器")
    print()
    
    print("🔧 可用的Midscene执行器:")
    if MIDSCENE_AVAILABLE:
        print("  - subprocess: 通过子进程调用Node.js脚本")
    else:
        print("  - subprocess: 不可用（需要Node.js环境和@midscene/android包）")
    
    if MIDSCENE_API_AVAILABLE:
        print("  - api: 通过HTTP API调用Node.js服务")
    else:
        print("  - api: 不可用（需要Node.js环境和相关依赖）")
    
    print()
    print("📦 安装要求:")
    print("  1. Node.js (v16+)")
    print("  2. npm install @midscene/android")
    print("  3. 连接的Android设备或模拟器")
    print("  4. 配置OpenAI API密钥（用于AI功能）")


def main():
    """主函数"""
    show_midscene_info()
    
    # 从命令行参数获取配置
    executor_type = "api"  # 默认使用API方式
    device_id = None
    demo_type = "features"  # 默认演示功能
    
    if len(sys.argv) > 1:
        executor_type = sys.argv[1]
    if len(sys.argv) > 2:
        device_id = sys.argv[2]
    if len(sys.argv) > 3:
        demo_type = sys.argv[3]
    
    print(f"\n🎯 使用执行器类型: {executor_type}")
    if device_id:
        print(f"📱 目标设备: {device_id}")
    
    # 创建执行器
    executor = create_midscene_executor(executor_type, device_id)
    if not executor:
        print("❌ 执行器创建失败，退出程序")
        return
    
    try:
        if demo_type == "features":
            # 测试基本功能
            test_midscene_features(executor)
        elif demo_type == "ebay":
            # 运行eBay演示
            run_ebay_demo(executor)
        elif demo_type == "exploration":
            # 运行完整探索
            run_app_exploration_with_midscene(executor)
        else:
            print(f"❌ 不支持的演示类型: {demo_type}")
            print("支持的类型: features, ebay, exploration")
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断演示")
    except Exception as e:
        print(f"\n💥 演示过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if hasattr(executor, '__del__'):
            try:
                executor.__del__()
            except:
                pass


if __name__ == "__main__":
    main()
