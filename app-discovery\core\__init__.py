"""
Core module for app discovery system.
"""

from .models import Page<PERSON>reeNode, UIElement
from .executor import ExecutorInterface, MockExecutor, ActionResult, PageInfo, ScreenshotResult
from .page_manager import PageNodeManager

# 可选的执行器实现
try:
    from .adb_executor import ADBExecutor
    ADB_AVAILABLE = True
except ImportError:
    ADBExecutor = None
    ADB_AVAILABLE = False

try:
    from .uiautomator2_executor import UIAutomator2Executor
    UIAUTOMATOR2_AVAILABLE = True
except ImportError:
    UIAutomator2Executor = None
    UIAUTOMATOR2_AVAILABLE = False

try:
    from .midscene_executor import MidsceneExecutor
    MIDSCENE_AVAILABLE = True
except ImportError:
    MidsceneExecutor = None
    MIDSCENE_AVAILABLE = False

try:
    from .midscene_api_executor import MidsceneAPIExecutor
    MIDSCENE_API_AVAILABLE = True
except ImportError:
    MidsceneAPIExecutor = None
    MIDSCENE_API_AVAILABLE = False

__all__ = [
    'PageTreeNode',
    'UIElement',
    'ExecutorInterface',
    'MockExecutor',
    'ActionResult',
    'PageInfo',
    'ScreenshotResult',
    'PageNodeManager'
]

# 添加可用的执行器
if ADB_AVAILABLE:
    __all__.append('ADBExecutor')
if UIAUTOMATOR2_AVAILABLE:
    __all__.append('UIAutomator2Executor')
if MIDSCENE_AVAILABLE:
    __all__.append('MidsceneExecutor')
if MIDSCENE_API_AVAILABLE:
    __all__.append('MidsceneAPIExecutor')
