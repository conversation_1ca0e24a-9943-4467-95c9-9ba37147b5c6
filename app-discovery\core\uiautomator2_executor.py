"""
基于uiautomator2的Android应用执行器实现
需要安装: pip install uiautomator2
"""
import time
import base64
import os
from typing import Dict, Any, Optional, List
from datetime import datetime

try:
    import uiautomator2 as u2
    UIAUTOMATOR2_AVAILABLE = True
except ImportError:
    UIAUTOMATOR2_AVAILABLE = False
    u2 = None

from .executor import ExecutorInterface, ActionResult, PageInfo, ScreenshotResult, UIElement


class UIAutomator2Executor(ExecutorInterface):
    """基于uiautomator2的Android应用执行器"""
    
    def __init__(self, device_id: str = None):
        """
        初始化UIAutomator2执行器
        
        Args:
            device_id: Android设备ID，如果为None则使用默认设备
        """
        if not UIAUTOMATOR2_AVAILABLE:
            raise ImportError("uiautomator2 not available. Install with: pip install uiautomator2")
        
        self.device_id = device_id
        self.device = u2.connect(device_id) if device_id else u2.connect()
        self.current_package = None
        self.screenshots_dir = "./screenshots"
        
        # 确保截图目录存在
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # 验证设备连接
        self._verify_device_connection()
    
    def _verify_device_connection(self):
        """验证设备连接"""
        try:
            info = self.device.info
            print(f"✅ 连接到设备: {info.get('productName', 'Unknown')}")
        except Exception as e:
            raise Exception(f"设备连接失败: {str(e)}")
    
    def install_and_launch_app(self, apk_path: str) -> ActionResult:
        """安装并启动应用"""
        try:
            print(f"📱 正在安装APK: {apk_path}")
            
            # 安装APK
            self.device.app_install(apk_path)
            
            # 获取包名
            package_name = self._get_package_name_from_device(apk_path)
            if not package_name:
                return ActionResult(
                    success=False,
                    message="无法获取应用包名"
                )
            
            self.current_package = package_name
            
            # 启动应用
            print(f"🚀 正在启动应用: {package_name}")
            self.device.app_start(package_name)
            
            # 等待应用启动
            time.sleep(3)
            
            # 获取当前页面信息
            page_info = self.get_current_page_info()
            
            return ActionResult(
                success=True,
                message=f"成功安装并启动应用: {package_name}",
                new_page_info=page_info
            )
            
        except Exception as e:
            return ActionResult(
                success=False,
                message=f"安装启动应用失败: {str(e)}",
                error_details=str(e)
            )
    
    def _get_package_name_from_device(self, apk_path: str) -> Optional[str]:
        """从设备获取已安装应用的包名"""
        try:
            # 获取所有已安装的应用
            apps = self.device.app_list()
            
            # 这里需要更智能的方法来匹配APK和包名
            # 简化实现：返回最近安装的应用
            if apps:
                return apps[-1]  # 返回列表中的最后一个应用
            
            return None
        except Exception:
            return None
    
    def get_current_page_info(self) -> PageInfo:
        """获取当前页面信息"""
        try:
            # 获取截图
            screenshot = self._take_screenshot()
            
            # 获取UI元素
            elements = self._get_ui_elements()
            
            # 获取当前应用信息
            current_app = self.device.app_current()
            
            return PageInfo(
                screenshot=screenshot,
                elements=elements,
                activity_name=current_app.get("activity"),
                package_name=current_app.get("package"),
                page_source=None
            )
            
        except Exception as e:
            print(f"获取页面信息失败: {str(e)}")
            return PageInfo(
                screenshot=ScreenshotResult(
                    image_path="",
                    image_base64="",
                    width=0,
                    height=0,
                    timestamp=datetime.now().isoformat()
                ),
                elements=[],
                activity_name=None,
                package_name=self.current_package
            )
    
    def _take_screenshot(self) -> ScreenshotResult:
        """截取屏幕截图"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        local_path = os.path.join(self.screenshots_dir, f"screenshot_{timestamp}.png")
        
        try:
            # 截图并保存
            self.device.screenshot(local_path)
            
            # 获取图片尺寸
            width, height = self._get_image_size(local_path)
            
            # 转换为base64
            with open(local_path, "rb") as f:
                image_base64 = base64.b64encode(f.read()).decode()
            
            return ScreenshotResult(
                image_path=local_path,
                image_base64=image_base64,
                width=width,
                height=height,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            print(f"截图失败: {str(e)}")
            return ScreenshotResult(
                image_path="",
                image_base64="",
                width=0,
                height=0,
                timestamp=datetime.now().isoformat()
            )
    
    def _get_image_size(self, image_path: str) -> tuple:
        """获取图片尺寸"""
        try:
            from PIL import Image
            with Image.open(image_path) as img:
                return img.size
        except:
            # 如果PIL不可用，从设备获取屏幕尺寸
            try:
                info = self.device.info
                return (info.get("displayWidth", 1080), info.get("displayHeight", 1920))
            except:
                return (1080, 1920)
    
    def _get_ui_elements(self) -> List[UIElement]:
        """获取UI元素信息"""
        try:
            # 获取UI层次结构
            xml_content = self.device.dump_hierarchy()
            
            # 解析XML获取UI元素
            elements = self._parse_ui_xml(xml_content)
            
            return elements
            
        except Exception as e:
            print(f"获取UI元素失败: {str(e)}")
            return []
    
    def _parse_ui_xml(self, xml_content: str) -> List[UIElement]:
        """解析UI XML内容"""
        elements = []
        try:
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_content)
            
            for node in root.iter():
                if node.tag == "node":
                    bounds_str = node.get("bounds", "")
                    bounds = self._parse_bounds(bounds_str)
                    
                    element = UIElement(
                        type=node.get("class", "").split(".")[-1].lower(),
                        text=node.get("text"),
                        hint=node.get("content-desc"),
                        bounds=bounds,
                        clickable=node.get("clickable") == "true",
                        scrollable=node.get("scrollable") == "true",
                        attributes={
                            "resource-id": node.get("resource-id"),
                            "package": node.get("package"),
                            "enabled": node.get("enabled"),
                            "focused": node.get("focused"),
                            "selected": node.get("selected")
                        }
                    )
                    elements.append(element)
            
        except Exception as e:
            print(f"解析UI XML失败: {str(e)}")
        
        return elements
    
    def _parse_bounds(self, bounds_str: str) -> Optional[List[int]]:
        """解析bounds字符串，格式: [x1,y1][x2,y2]"""
        try:
            import re
            matches = re.findall(r'\[(\d+),(\d+)\]', bounds_str)
            if len(matches) == 2:
                x1, y1 = map(int, matches[0])
                x2, y2 = map(int, matches[1])
                return [x1, y1, x2, y2]
        except:
            pass
        return None

    def execute_action(self, action_description: str) -> ActionResult:
        """根据自然语言描述执行操作"""
        try:
            print(f"🎯 执行操作: {action_description}")

            # 使用uiautomator2的智能查找和操作
            action_lower = action_description.lower()

            # 点击操作
            if any(keyword in action_lower for keyword in ["点击", "click", "tap", "按"]):
                return self._execute_smart_click(action_description)

            # 输入操作
            elif any(keyword in action_lower for keyword in ["输入", "input", "type", "填写"]):
                return self._execute_smart_input(action_description)

            # 滚动操作
            elif any(keyword in action_lower for keyword in ["滚动", "scroll", "滑动", "swipe"]):
                return self._execute_smart_scroll(action_description)

            # 返回操作
            elif any(keyword in action_lower for keyword in ["返回", "back", "后退"]):
                return self.go_back()

            # 等待操作
            elif any(keyword in action_lower for keyword in ["等待", "wait", "sleep"]):
                wait_time = self._parse_wait_time(action_description)
                time.sleep(wait_time)
                return ActionResult(success=True, message=f"等待了 {wait_time} 秒")

            else:
                # 默认尝试智能点击
                return self._execute_smart_click(action_description)

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"执行操作失败: {str(e)}",
                error_details=str(e)
            )

    def _execute_smart_click(self, description: str) -> ActionResult:
        """使用uiautomator2的智能点击"""
        try:
            # 提取关键词
            keywords = self._extract_keywords(description)

            # 尝试多种查找方式
            element = None

            for keyword in keywords:
                # 按文本查找
                if self.device(text=keyword).exists:
                    element = self.device(text=keyword)
                    break

                # 按描述查找
                if self.device(description=keyword).exists:
                    element = self.device(description=keyword)
                    break

                # 按部分文本查找
                if self.device(textContains=keyword).exists:
                    element = self.device(textContains=keyword)
                    break

            # 如果没找到，尝试按类型查找
            if not element:
                if "按钮" in description or "button" in description.lower():
                    if self.device(className="android.widget.Button").exists:
                        element = self.device(className="android.widget.Button")
                elif "输入" in description or "input" in description.lower():
                    if self.device(className="android.widget.EditText").exists:
                        element = self.device(className="android.widget.EditText")

            if element and element.exists:
                element.click()
                time.sleep(1)  # 等待界面响应

                # 获取新的页面信息
                new_page_info = self.get_current_page_info()

                return ActionResult(
                    success=True,
                    message=f"成功点击元素: {description}",
                    new_page_info=new_page_info
                )
            else:
                return ActionResult(
                    success=False,
                    message=f"未找到匹配的元素: {description}"
                )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"智能点击失败: {str(e)}",
                error_details=str(e)
            )

    def _execute_smart_input(self, description: str) -> ActionResult:
        """使用uiautomator2的智能输入"""
        try:
            # 提取要输入的文本
            import re
            text_match = re.search(r'["\']([^"\']+)["\']', description)
            if text_match:
                input_text = text_match.group(1)
            else:
                input_match = re.search(r'输入\s*(.+)', description)
                if input_match:
                    input_text = input_match.group(1).strip()
                else:
                    return ActionResult(
                        success=False,
                        message="无法从描述中提取要输入的文本"
                    )

            # 查找输入框
            input_element = None

            # 尝试多种方式查找输入框
            if self.device(className="android.widget.EditText").exists:
                input_element = self.device(className="android.widget.EditText")
            elif self.device(focusable=True, clickable=True).exists:
                # 查找可聚焦的元素
                elements = self.device(focusable=True, clickable=True)
                if elements.exists:
                    input_element = elements

            if input_element and input_element.exists:
                # 点击输入框获得焦点
                input_element.click()
                time.sleep(0.5)

                # 清除现有文本并输入新文本
                input_element.clear_text()
                input_element.set_text(input_text)

                time.sleep(1)  # 等待输入完成

                # 获取新的页面信息
                new_page_info = self.get_current_page_info()

                return ActionResult(
                    success=True,
                    message=f"成功输入文本: {input_text}",
                    new_page_info=new_page_info
                )
            else:
                return ActionResult(
                    success=False,
                    message="未找到可输入的元素"
                )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"智能输入失败: {str(e)}",
                error_details=str(e)
            )

    def _execute_smart_scroll(self, description: str) -> ActionResult:
        """使用uiautomator2的智能滚动"""
        try:
            description_lower = description.lower()

            if any(word in description_lower for word in ["向上", "up"]):
                direction = "up"
            elif any(word in description_lower for word in ["向下", "down"]):
                direction = "down"
            elif any(word in description_lower for word in ["向左", "left"]):
                direction = "left"
            elif any(word in description_lower for word in ["向右", "right"]):
                direction = "right"
            else:
                direction = "down"  # 默认向下滚动

            return self.scroll(direction)

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"智能滚动失败: {str(e)}",
                error_details=str(e)
            )

    def _extract_keywords(self, description: str) -> List[str]:
        """从描述中提取关键词"""
        # 移除操作词汇
        stop_words = ["点击", "click", "tap", "按", "按钮", "button"]
        words = description.split()

        keywords = []
        for word in words:
            if word not in stop_words and len(word) > 1:
                keywords.append(word)

        return keywords

    def _parse_wait_time(self, description: str) -> float:
        """解析等待时间"""
        import re
        time_match = re.search(r'(\d+(?:\.\d+)?)', description)
        if time_match:
            return float(time_match.group(1))
        else:
            return 2.0  # 默认等待2秒

    def click_element(self, x: int, y: int) -> ActionResult:
        """点击指定坐标"""
        try:
            print(f"👆 点击坐标: ({x}, {y})")
            self.device.click(x, y)

            time.sleep(1)  # 等待界面响应

            # 获取新的页面信息
            new_page_info = self.get_current_page_info()

            return ActionResult(
                success=True,
                message=f"成功点击坐标 ({x}, {y})",
                new_page_info=new_page_info
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"点击操作失败: {str(e)}",
                error_details=str(e)
            )

    def input_text(self, text: str, element_bounds: Optional[List[int]] = None) -> ActionResult:
        """输入文本"""
        try:
            print(f"⌨️ 输入文本: {text}")

            # 如果提供了元素边界，先点击该区域
            if element_bounds:
                x1, y1, x2, y2 = element_bounds
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                self.device.click(center_x, center_y)
                time.sleep(0.5)

            # 查找当前聚焦的输入框
            if self.device(focused=True).exists:
                focused_element = self.device(focused=True)
                focused_element.clear_text()
                focused_element.set_text(text)
            else:
                # 查找第一个可用的输入框
                if self.device(className="android.widget.EditText").exists:
                    input_element = self.device(className="android.widget.EditText")
                    input_element.click()
                    time.sleep(0.5)
                    input_element.clear_text()
                    input_element.set_text(text)
                else:
                    return ActionResult(
                        success=False,
                        message="未找到可输入的元素"
                    )

            time.sleep(1)  # 等待输入完成

            # 获取新的页面信息
            new_page_info = self.get_current_page_info()

            return ActionResult(
                success=True,
                message=f"成功输入文本: {text}",
                new_page_info=new_page_info
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"输入文本失败: {str(e)}",
                error_details=str(e)
            )

    def scroll(self, direction: str = "down", distance: int = 500) -> ActionResult:
        """滚动页面"""
        try:
            print(f"📜 滚动: {direction}")

            if direction == "down":
                self.device.swipe_ext("up")
            elif direction == "up":
                self.device.swipe_ext("down")
            elif direction == "left":
                self.device.swipe_ext("right")
            elif direction == "right":
                self.device.swipe_ext("left")
            else:
                return ActionResult(
                    success=False,
                    message=f"不支持的滚动方向: {direction}"
                )

            time.sleep(1)  # 等待滚动完成

            # 获取新的页面信息
            new_page_info = self.get_current_page_info()

            return ActionResult(
                success=True,
                message=f"成功向{direction}滚动",
                new_page_info=new_page_info
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"滚动操作失败: {str(e)}",
                error_details=str(e)
            )

    def go_back(self) -> ActionResult:
        """返回上一页"""
        try:
            print("⬅️ 返回上一页")
            self.device.press("back")

            time.sleep(1)  # 等待页面切换

            # 获取新的页面信息
            new_page_info = self.get_current_page_info()

            return ActionResult(
                success=True,
                message="成功返回上一页",
                new_page_info=new_page_info
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"返回操作失败: {str(e)}",
                error_details=str(e)
            )

    def close_app(self) -> ActionResult:
        """关闭应用"""
        try:
            if not self.current_package:
                return ActionResult(
                    success=False,
                    message="没有正在运行的应用"
                )

            print(f"🔴 关闭应用: {self.current_package}")
            self.device.app_stop(self.current_package)

            return ActionResult(
                success=True,
                message=f"成功关闭应用: {self.current_package}"
            )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"关闭应用失败: {str(e)}",
                error_details=str(e)
            )

    def is_app_running(self) -> bool:
        """检查应用是否正在运行"""
        try:
            if not self.current_package:
                return False

            current_app = self.device.app_current()
            return current_app.get("package") == self.current_package

        except Exception:
            return False
