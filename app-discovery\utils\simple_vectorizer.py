"""
简单的文本向量化器，用于页面相似性计算
"""
import re
import math
from typing import List, Dict, Set
from collections import Counter
import numpy as np


class SimpleTextVectorizer:
    """简单的文本向量化器，基于词频和TF-IDF"""
    
    def __init__(self, max_features: int = 1000):
        self.max_features = max_features
        self.vocabulary = {}
        self.idf_scores = {}
        self.fitted = False
    
    def _preprocess_text(self, text: str) -> List[str]:
        """预处理文本，分词并清理"""
        # 转换为小写
        text = text.lower()
        # 移除特殊字符，保留中文、英文和数字
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', ' ', text)
        # 分词（简单按空格分割，对中文不够精确但足够演示）
        words = text.split()
        # 过滤空词和单字符词
        words = [word for word in words if len(word) > 1]
        return words
    
    def _build_vocabulary(self, documents: List[str]):
        """构建词汇表"""
        word_counts = Counter()
        doc_word_sets = []
        
        # 统计词频和文档频率
        for doc in documents:
            words = self._preprocess_text(doc)
            doc_word_sets.append(set(words))
            word_counts.update(words)
        
        # 选择最频繁的词作为特征
        most_common = word_counts.most_common(self.max_features)
        self.vocabulary = {word: idx for idx, (word, _) in enumerate(most_common)}
        
        # 计算IDF分数
        total_docs = len(documents)
        for word in self.vocabulary:
            doc_freq = sum(1 for word_set in doc_word_sets if word in word_set)
            self.idf_scores[word] = math.log(total_docs / (doc_freq + 1))
    
    def fit(self, documents: List[str]):
        """训练向量化器"""
        self._build_vocabulary(documents)
        self.fitted = True
    
    def transform(self, documents: List[str]) -> np.ndarray:
        """将文档转换为向量"""
        if not self.fitted:
            raise ValueError("Vectorizer must be fitted before transform")
        
        vectors = []
        for doc in documents:
            vector = self._document_to_vector(doc)
            vectors.append(vector)
        
        return np.array(vectors)
    
    def fit_transform(self, documents: List[str]) -> np.ndarray:
        """训练并转换文档"""
        self.fit(documents)
        return self.transform(documents)
    
    def _document_to_vector(self, document: str) -> np.ndarray:
        """将单个文档转换为向量"""
        words = self._preprocess_text(document)
        word_counts = Counter(words)
        
        # 创建TF-IDF向量
        vector = np.zeros(len(self.vocabulary))
        total_words = len(words)
        
        for word, count in word_counts.items():
            if word in self.vocabulary:
                idx = self.vocabulary[word]
                tf = count / total_words  # 词频
                idf = self.idf_scores.get(word, 0)  # 逆文档频率
                vector[idx] = tf * idf
        
        # 归一化向量
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        
        return vector
    
    def get_feature_names(self) -> List[str]:
        """获取特征名称"""
        if not self.fitted:
            return []
        
        features = [''] * len(self.vocabulary)
        for word, idx in self.vocabulary.items():
            features[idx] = word
        return features


def cosine_similarity_simple(vec1: np.ndarray, vec2: np.ndarray) -> float:
    """计算两个向量的余弦相似度"""
    dot_product = np.dot(vec1, vec2)
    norm1 = np.linalg.norm(vec1)
    norm2 = np.linalg.norm(vec2)
    
    if norm1 == 0 or norm2 == 0:
        return 0.0
    
    return dot_product / (norm1 * norm2)


# 测试函数
def test_vectorizer():
    """测试向量化器"""
    documents = [
        "登录页面，包含用户名和密码输入框",
        "应用主页，显示欢迎信息和导航按钮",
        "设置页面，包含各种配置选项",
        "用户登录界面",
        "配置选项和设置"
    ]
    
    vectorizer = SimpleTextVectorizer(max_features=50)
    vectors = vectorizer.fit_transform(documents)
    
    print("词汇表:", vectorizer.get_feature_names())
    print("向量形状:", vectors.shape)
    
    # 测试相似度
    query = "用户登录"
    query_vector = vectorizer._document_to_vector(query)
    
    print(f"\n查询: '{query}'")
    for i, doc in enumerate(documents):
        similarity = cosine_similarity_simple(query_vector, vectors[i])
        print(f"与文档 {i+1} 的相似度: {similarity:.3f} - {doc}")


if __name__ == "__main__":
    test_vectorizer()
