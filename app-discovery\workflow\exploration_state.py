"""
LangGraph状态管理 - 定义探索过程中的状态
"""
from typing import Dict, Any, List, Optional, TypedDict
from dataclasses import dataclass, field
from datetime import datetime
import json

from core.executor import PageInfo, ActionResult
from core.models import PageTreeNode


@dataclass
class ExplorationStep:
    """探索步骤记录"""
    step_id: int
    timestamp: str
    page_id: str
    action_description: str
    action_result: Optional[ActionResult] = None
    page_description: str = ""
    is_new_page: bool = False
    similarity_score: float = 0.0


@dataclass
class ExplorationGoal:
    """探索目标"""
    description: str
    target_features: List[str] = field(default_factory=list)
    completion_criteria: List[str] = field(default_factory=list)
    priority: int = 1  # 1-5, 5最高


class ExplorationState(TypedDict):
    """LangGraph状态定义"""
    
    # 基本信息
    apk_path: str
    exploration_goal: ExplorationGoal
    session_id: str
    start_time: str
    
    # 当前状态
    current_page_info: Optional[PageInfo]
    current_page_id: Optional[str]
    current_step: int
    
    # 探索历史
    exploration_steps: List[ExplorationStep]
    visited_pages: Dict[str, PageTreeNode]  # page_id -> PageTreeNode
    page_similarities: Dict[str, float]  # page_id -> similarity to goal
    
    # 决策状态
    next_action_plan: str
    available_actions: List[str]
    completed_goals: List[str]
    
    # 错误和重试
    last_error: Optional[str]
    retry_count: int
    max_retries: int
    
    # 终止条件
    should_continue: bool
    termination_reason: Optional[str]
    
    # 报告数据
    exploration_report: Dict[str, Any]


def create_initial_state(
    apk_path: str,
    exploration_goal: ExplorationGoal,
    session_id: Optional[str] = None,
    max_retries: int = 3
) -> ExplorationState:
    """创建初始探索状态"""
    
    if session_id is None:
        session_id = f"exploration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    return ExplorationState(
        # 基本信息
        apk_path=apk_path,
        exploration_goal=exploration_goal,
        session_id=session_id,
        start_time=datetime.now().isoformat(),
        
        # 当前状态
        current_page_info=None,
        current_page_id=None,
        current_step=0,
        
        # 探索历史
        exploration_steps=[],
        visited_pages={},
        page_similarities={},
        
        # 决策状态
        next_action_plan="",
        available_actions=[],
        completed_goals=[],
        
        # 错误和重试
        last_error=None,
        retry_count=0,
        max_retries=max_retries,
        
        # 终止条件
        should_continue=True,
        termination_reason=None,
        
        # 报告数据
        exploration_report={}
    )


def update_state_with_page_info(state: ExplorationState, page_info: PageInfo, page_id: str) -> ExplorationState:
    """更新状态中的页面信息"""
    state["current_page_info"] = page_info
    state["current_page_id"] = page_id
    return state


def add_exploration_step(
    state: ExplorationState,
    action_description: str,
    action_result: Optional[ActionResult] = None,
    page_description: str = "",
    is_new_page: bool = False,
    similarity_score: float = 0.0
) -> ExplorationState:
    """添加探索步骤"""
    
    step = ExplorationStep(
        step_id=state["current_step"] + 1,
        timestamp=datetime.now().isoformat(),
        page_id=state["current_page_id"] or "unknown",
        action_description=action_description,
        action_result=action_result,
        page_description=page_description,
        is_new_page=is_new_page,
        similarity_score=similarity_score
    )
    
    state["exploration_steps"].append(step)
    state["current_step"] = step.step_id
    
    return state


def add_visited_page(state: ExplorationState, page_id: str, page_node: PageTreeNode) -> ExplorationState:
    """添加已访问页面"""
    state["visited_pages"][page_id] = page_node
    return state


def set_termination(state: ExplorationState, reason: str) -> ExplorationState:
    """设置终止状态"""
    state["should_continue"] = False
    state["termination_reason"] = reason
    return state


def increment_retry(state: ExplorationState, error_message: str) -> ExplorationState:
    """增加重试计数"""
    state["retry_count"] += 1
    state["last_error"] = error_message
    
    if state["retry_count"] >= state["max_retries"]:
        state = set_termination(state, f"达到最大重试次数: {error_message}")
    
    return state


def reset_retry(state: ExplorationState) -> ExplorationState:
    """重置重试计数"""
    state["retry_count"] = 0
    state["last_error"] = None
    return state


def get_exploration_summary(state: ExplorationState) -> Dict[str, Any]:
    """获取探索摘要"""
    total_steps = len(state["exploration_steps"])
    unique_pages = len(state["visited_pages"])
    successful_actions = sum(1 for step in state["exploration_steps"] 
                           if step.action_result and step.action_result.success)
    
    return {
        "session_id": state["session_id"],
        "start_time": state["start_time"],
        "end_time": datetime.now().isoformat(),
        "total_steps": total_steps,
        "unique_pages_visited": unique_pages,
        "successful_actions": successful_actions,
        "success_rate": successful_actions / total_steps if total_steps > 0 else 0,
        "exploration_goal": state["exploration_goal"].description,
        "completed_goals": state["completed_goals"],
        "termination_reason": state["termination_reason"],
        "final_page": state["current_page_id"]
    }


def state_to_dict(state: ExplorationState) -> Dict[str, Any]:
    """将状态转换为可序列化的字典"""
    result = dict(state)
    
    # 转换复杂对象
    if result["exploration_goal"]:
        result["exploration_goal"] = {
            "description": result["exploration_goal"].description,
            "target_features": result["exploration_goal"].target_features,
            "completion_criteria": result["exploration_goal"].completion_criteria,
            "priority": result["exploration_goal"].priority
        }
    
    # 转换探索步骤
    result["exploration_steps"] = [
        {
            "step_id": step.step_id,
            "timestamp": step.timestamp,
            "page_id": step.page_id,
            "action_description": step.action_description,
            "page_description": step.page_description,
            "is_new_page": step.is_new_page,
            "similarity_score": step.similarity_score,
            "action_success": step.action_result.success if step.action_result else None
        }
        for step in result["exploration_steps"]
    ]
    
    # 转换页面节点
    result["visited_pages"] = {
        page_id: node.to_dict() 
        for page_id, node in result["visited_pages"].items()
    }
    
    # 移除不可序列化的对象
    result["current_page_info"] = None  # PageInfo对象包含复杂数据
    
    return result


def save_state_to_file(state: ExplorationState, file_path: str):
    """保存状态到文件"""
    state_dict = state_to_dict(state)
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(state_dict, f, ensure_ascii=False, indent=2)


def load_state_from_file(file_path: str) -> Dict[str, Any]:
    """从文件加载状态"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)
