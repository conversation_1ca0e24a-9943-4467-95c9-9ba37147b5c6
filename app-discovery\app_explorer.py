"""
应用探索主程序 - 基于LangGraph的自动化应用探索系统
"""
import os
import sys
from typing import Optional
from datetime import datetime

from executor import MockExecutor, ExecutorInterface
from exploration_workflow import run_app_exploration
from report_generator import generate_exploration_report
from page_manager import PageNodeManager


class AppExplorer:
    """应用探索器主类"""
    
    def __init__(
        self,
        executor: ExecutorInterface,
        llm_model: str = "gpt-4",
        page_manager: Optional[PageNodeManager] = None
    ):
        self.executor = executor
        self.llm_model = llm_model
        self.page_manager = page_manager or PageNodeManager(
            collection_name="app_exploration",
            persist_directory="./chroma_exploration"
        )
    
    def explore_app(
        self,
        apk_path: str,
        goal_description: str,
        target_features: list = None,
        completion_criteria: list = None,
        max_retries: int = 3,
        generate_report: bool = True,
        report_output_path: str = None
    ) -> dict:
        """
        探索应用的主要方法
        
        Args:
            apk_path: APK文件路径
            goal_description: 探索目标描述
            target_features: 目标功能列表
            completion_criteria: 完成标准列表
            max_retries: 最大重试次数
            generate_report: 是否生成报告
            report_output_path: 报告输出路径
            
        Returns:
            dict: 探索结果
        """
        
        print("🚀 启动应用探索系统")
        print("=" * 60)
        print(f"📱 目标应用: {apk_path}")
        print(f"🎯 探索目标: {goal_description}")
        print(f"🤖 使用模型: {self.llm_model}")
        print("=" * 60)
        
        try:
            # 运行探索工作流
            result = run_app_exploration(
                apk_path=apk_path,
                goal_description=goal_description,
                executor=self.executor,
                target_features=target_features or [],
                completion_criteria=completion_criteria or [],
                max_retries=max_retries,
                llm_model=self.llm_model
            )
            
            # 打印探索结果摘要
            self._print_exploration_summary(result)
            
            # 生成详细报告
            if generate_report and result.get("success"):
                report_path = report_output_path or self._generate_report_path(result.get("session_id"))
                report_content = generate_exploration_report(
                    result,
                    output_path=report_path,
                    format_type="markdown",
                    llm_model=self.llm_model
                )
                result["report_path"] = report_path
                result["report_content"] = report_content
            
            return result
            
        except Exception as e:
            error_msg = f"探索过程中发生异常: {str(e)}"
            print(f"❌ {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "timestamp": datetime.now().isoformat()
            }
    
    def _print_exploration_summary(self, result: dict):
        """打印探索结果摘要"""
        print("\n" + "=" * 60)
        print("📊 探索结果摘要")
        print("=" * 60)
        
        if result.get("success"):
            print(f"✅ 探索成功完成")
            print(f"🆔 会话ID: {result.get('session_id', 'N/A')}")
            print(f"📈 总步数: {result.get('total_steps', 0)}")
            print(f"📱 发现页面: {result.get('pages_discovered', 0)}")
            print(f"🏁 终止原因: {result.get('termination_reason', 'N/A')}")
        else:
            print(f"❌ 探索失败")
            print(f"🚫 错误信息: {result.get('error', 'N/A')}")
        
        print("=" * 60)
    
    def _generate_report_path(self, session_id: str) -> str:
        """生成报告文件路径"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"exploration_report_{session_id or timestamp}.md"
        
        # 确保reports目录存在
        reports_dir = "./reports"
        os.makedirs(reports_dir, exist_ok=True)
        
        return os.path.join(reports_dir, filename)


def create_demo_scenarios():
    """创建演示场景"""
    return [
        {
            "name": "电商应用探索",
            "apk_path": "/path/to/ecommerce_app.apk",
            "goal": "探索电商应用的完整购物流程，包括商品浏览、加入购物车、结算等功能",
            "target_features": ["商品搜索", "购物车", "用户登录", "订单管理"],
            "completion_criteria": ["成功浏览商品", "添加商品到购物车", "访问用户中心"]
        },
        {
            "name": "社交应用探索",
            "apk_path": "/path/to/social_app.apk",
            "goal": "探索社交应用的核心功能，包括用户注册、发布内容、社交互动等",
            "target_features": ["用户注册", "发布动态", "好友功能", "消息聊天"],
            "completion_criteria": ["完成用户注册", "发布一条动态", "查看好友列表"]
        },
        {
            "name": "工具应用探索",
            "apk_path": "/path/to/utility_app.apk",
            "goal": "探索工具类应用的主要功能和设置选项",
            "target_features": ["主要工具功能", "设置选项", "帮助文档"],
            "completion_criteria": ["使用核心功能", "访问设置页面", "查看帮助信息"]
        }
    ]


def run_demo_exploration():
    """运行演示探索"""
    print("🎮 应用探索系统演示")
    print("使用Mock Executor进行演示...")
    
    # 创建Mock执行器
    executor = MockExecutor()
    
    # 创建应用探索器
    explorer = AppExplorer(
        executor=executor,
        llm_model="gpt-4"  # 在实际使用中需要配置API密钥
    )
    
    # 运行演示场景
    demo_scenario = {
        "apk_path": "/demo/shopping_app.apk",
        "goal": "探索购物应用的登录流程和主要功能页面",
        "target_features": ["用户登录", "商品浏览", "购物车", "个人中心"],
        "completion_criteria": ["成功登录", "访问主页", "查看商品列表"]
    }
    
    result = explorer.explore_app(
        apk_path=demo_scenario["apk_path"],
        goal_description=demo_scenario["goal"],
        target_features=demo_scenario["target_features"],
        completion_criteria=demo_scenario["completion_criteria"],
        max_retries=3,
        generate_report=True
    )
    
    return result


def main():
    """主函数"""
    print("🤖 基于LangGraph的应用自动探索系统")
    print("=" * 60)
    
    # 检查环境变量
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️ 警告: 未设置OPENAI_API_KEY环境变量")
        print("   将使用Mock模式进行演示")
        print()
    
    try:
        # 运行演示
        result = run_demo_exploration()
        
        if result.get("success"):
            print("\n🎉 演示完成!")
            if result.get("report_path"):
                print(f"📄 详细报告已生成: {result['report_path']}")
        else:
            print(f"\n❌ 演示失败: {result.get('error')}")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断探索")
    except Exception as e:
        print(f"\n💥 系统异常: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
