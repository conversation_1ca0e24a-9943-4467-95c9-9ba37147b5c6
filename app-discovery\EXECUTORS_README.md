# Android应用执行器实现指南

本项目提供了多种Android应用自动化执行器实现，包括基于Midscene Android SDK的AI驱动执行器。

## 🚀 执行器类型

### 1. MockExecutor（演示模式）
- **文件**: `core/executor.py`
- **用途**: 演示和测试，无需真实设备
- **特点**: 模拟操作响应，适合开发调试

### 2. ADBExecutor（ADB命令行）
- **文件**: `core/adb_executor.py`
- **用途**: 通过ADB命令控制Android设备
- **依赖**: ADB工具，连接的Android设备
- **特点**: 直接的设备控制，兼容性好

### 3. UIAutomator2Executor（UIAutomator2）
- **文件**: `core/uiautomator2_executor.py`
- **用途**: 基于uiautomator2的Python库
- **依赖**: `pip install uiautomator2`
- **特点**: Python原生，功能丰富

### 4. MidsceneExecutor（Midscene子进程）
- **文件**: `core/midscene_executor.py`
- **用途**: 通过子进程调用Midscene TypeScript代码
- **依赖**: Node.js, @midscene/android
- **特点**: AI驱动，自然语言操作

### 5. MidsceneAPIExecutor（Midscene HTTP API）
- **文件**: `core/midscene_api_executor.py`
- **用途**: 通过HTTP API调用Midscene服务
- **依赖**: Node.js, @midscene/android, Express
- **特点**: 服务化部署，更好的进程管理

## 🛠️ 安装和配置

### 基础环境
```bash
# Python依赖
uv add chromadb numpy scikit-learn
uv add langgraph langchain langchain-openai

# 可选：UIAutomator2
pip install uiautomator2

# 可选：PIL（用于图片处理）
pip install Pillow
```

### Midscene环境配置

#### 1. 安装Node.js
```bash
# 安装Node.js (v16+)
# 下载地址: https://nodejs.org/
```

#### 2. 安装Midscene Android SDK
```bash
# 在项目目录下
mkdir midscene_scripts  # 或 midscene_api
cd midscene_scripts
npm init -y
npm install @midscene/android dotenv
```

#### 3. 配置环境变量
```bash
# 创建 .env 文件
echo "OPENAI_API_KEY=your_openai_api_key" > .env
```

#### 4. 连接Android设备
```bash
# 启用开发者选项和USB调试
adb devices  # 确认设备连接
```

## 📱 使用示例

### 基础使用
```python
from core import MidsceneAPIExecutor
from app_explorer import AppExplorer

# 创建执行器
executor = MidsceneAPIExecutor(device_id="your_device_id")

# 创建应用探索器
explorer = AppExplorer(executor=executor)

# 运行探索
result = explorer.explore_app(
    apk_path="com.example.app",
    goal_description="探索应用的登录流程",
    target_features=["登录", "注册", "主页"],
    completion_criteria=["成功登录", "访问主页"]
)
```

### Midscene AI功能
```python
# AI查询
items = executor.ai_query(
    "找到页面上所有商品，返回 {name: string, price: number}[]"
)

# AI等待
success = executor.ai_wait_for("页面加载完成", timeout=10)

# AI断言
executor.ai_assert("页面上有搜索按钮")

# 自然语言操作
executor.execute_action("在搜索框中输入'耳机'并点击搜索")
```

### 运行演示
```bash
# 基础功能演示
python midscene_demo.py api

# eBay搜索演示
python midscene_demo.py api your_device_id ebay

# 完整应用探索
python midscene_demo.py api your_device_id exploration
```

## 🔧 执行器对比

| 特性 | Mock | ADB | UIAutomator2 | Midscene |
|------|------|-----|--------------|----------|
| 真实设备 | ❌ | ✅ | ✅ | ✅ |
| AI驱动 | ❌ | ❌ | ❌ | ✅ |
| 自然语言 | ❌ | ❌ | ❌ | ✅ |
| 安装复杂度 | 低 | 中 | 中 | 高 |
| 性能 | 高 | 中 | 高 | 中 |
| 智能程度 | 低 | 低 | 中 | 高 |

## 🎯 推荐使用场景

### MockExecutor
- 开发和调试阶段
- CI/CD测试环境
- 功能演示

### ADBExecutor
- 简单的自动化任务
- 兼容性要求高的场景
- 资源受限环境

### UIAutomator2Executor
- 复杂的UI自动化
- 需要精确控制的场景
- Python生态集成

### MidsceneExecutor/MidsceneAPIExecutor
- AI驱动的智能测试
- 自然语言描述的操作
- 复杂应用探索
- 需要理解页面内容的场景

## 🚨 注意事项

### Midscene使用注意
1. **API密钥**: 需要配置OpenAI API密钥
2. **网络要求**: 需要访问OpenAI API
3. **设备要求**: Android 7.0+
4. **性能**: AI调用会增加延迟
5. **成本**: OpenAI API调用产生费用

### 通用注意事项
1. **权限**: 确保应用有必要的权限
2. **稳定性**: 网络不稳定可能影响执行
3. **兼容性**: 不同Android版本可能有差异
4. **资源**: 长时间运行注意内存和电量

## 🔍 故障排除

### 常见问题

#### 1. 设备连接失败
```bash
# 检查ADB连接
adb devices

# 重启ADB服务
adb kill-server
adb start-server
```

#### 2. Node.js依赖问题
```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
npm install
```

#### 3. Midscene API调用失败
- 检查OpenAI API密钥配置
- 确认网络连接
- 查看API服务日志

#### 4. 权限问题
- 确保USB调试已启用
- 检查应用权限设置
- 尝试手动授权

## 📚 扩展开发

### 自定义执行器
```python
from core.executor import ExecutorInterface, ActionResult

class CustomExecutor(ExecutorInterface):
    def execute_action(self, action_description: str) -> ActionResult:
        # 实现自定义逻辑
        pass
    
    # 实现其他必需方法...
```

### 添加新功能
1. 继承现有执行器类
2. 重写或扩展方法
3. 添加特定功能接口
4. 更新__init__.py导出

## 📖 参考资料

- [Midscene Android SDK](https://github.com/web-infra-dev/midscene)
- [UIAutomator2文档](https://github.com/openatx/uiautomator2)
- [ADB官方文档](https://developer.android.com/studio/command-line/adb)
- [LangGraph文档](https://langchain-ai.github.io/langgraph/)
