"""
基于HTTP API的Midscene执行器实现
通过启动Node.js HTTP服务来调用Midscene功能
"""
import requests
import subprocess
import time
import json
import os
import signal
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

from .executor import ExecutorInterface, ActionResult, PageInfo, ScreenshotResult, UIElement


class MidsceneAPIExecutor(ExecutorInterface):
    """基于HTTP API的Midscene执行器"""
    
    def __init__(self, device_id: str = None, api_port: int = 3000, node_script_dir: str = "./midscene_api"):
        """
        初始化Midscene API执行器
        
        Args:
            device_id: Android设备ID
            api_port: API服务端口
            node_script_dir: Node.js API服务目录
        """
        self.device_id = device_id
        self.api_port = api_port
        self.api_url = f"http://localhost:{api_port}"
        self.node_script_dir = Path(node_script_dir)
        self.current_package = None
        self.api_process = None
        
        # 设置Node.js API服务
        self._setup_api_service()
        
        # 启动API服务
        self._start_api_service()
        
        # 验证设备连接
        self._verify_device_connection()
    
    def _setup_api_service(self):
        """设置Node.js API服务"""
        self.node_script_dir.mkdir(exist_ok=True)
        
        # 创建package.json
        package_json_path = self.node_script_dir / "package.json"
        if not package_json_path.exists():
            package_json = {
                "name": "midscene-api-server",
                "version": "1.0.0",
                "type": "module",
                "dependencies": {
                    "@midscene/android": "latest",
                    "express": "latest",
                    "cors": "latest",
                    "dotenv": "latest"
                }
            }
            
            with open(package_json_path, 'w') as f:
                json.dump(package_json, f, indent=2)
        
        # 创建API服务器脚本
        server_script_path = self.node_script_dir / "server.js"
        if not server_script_path.exists():
            self._create_api_server_script(server_script_path)
        
        # 安装依赖
        if not (self.node_script_dir / "node_modules").exists():
            print("📦 安装Node.js依赖...")
            try:
                subprocess.run(
                    ["npm", "install"], 
                    cwd=self.node_script_dir, 
                    check=True,
                    capture_output=True
                )
                print("✅ Node.js依赖安装完成")
            except subprocess.CalledProcessError as e:
                raise Exception(f"Node.js依赖安装失败: {e.stderr.decode()}")
    
    def _create_api_server_script(self, script_path: Path):
        """创建API服务器脚本"""
        server_code = '''
import express from 'express';
import cors from 'cors';
import { AndroidAgent, AndroidDevice, getConnectedDevices } from '@midscene/android';

const app = express();
const PORT = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());

// 全局变量存储设备和代理
let devices = {};
let agents = {};

// 获取连接的设备
app.get('/api/devices', async (req, res) => {
    try {
        const connectedDevices = await getConnectedDevices();
        res.json({ success: true, devices: connectedDevices });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// 连接设备
app.post('/api/connect', async (req, res) => {
    try {
        const { device_id } = req.body;
        
        if (!devices[device_id]) {
            const page = new AndroidDevice(device_id);
            await page.connect();
            
            devices[device_id] = page;
            agents[device_id] = new AndroidAgent(page, {
                aiActionContext: 'Perform actions naturally and efficiently.'
            });
        }
        
        res.json({ success: true, message: `设备 ${device_id} 连接成功` });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// 启动应用
app.post('/api/launch', async (req, res) => {
    try {
        const { device_id, package_name, url } = req.body;
        const page = devices[device_id];
        
        if (!page) {
            return res.json({ success: false, error: '设备未连接' });
        }
        
        if (package_name) {
            await page.launch(package_name);
        } else if (url) {
            await page.launch(url);
        }
        
        // 等待启动
        await new Promise(r => setTimeout(r, 3000));
        
        res.json({ success: true, message: '应用启动成功' });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// 执行AI操作
app.post('/api/action', async (req, res) => {
    try {
        const { device_id, action_description } = req.body;
        const agent = agents[device_id];
        
        if (!agent) {
            return res.json({ success: false, error: '设备代理未初始化' });
        }
        
        const result = await agent.aiAction(action_description);
        
        res.json({ 
            success: true, 
            message: `操作执行成功: ${action_description}`,
            result: result
        });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// AI查询
app.post('/api/query', async (req, res) => {
    try {
        const { device_id, query } = req.body;
        const agent = agents[device_id];
        
        if (!agent) {
            return res.json({ success: false, error: '设备代理未初始化' });
        }
        
        const result = await agent.aiQuery(query);
        
        res.json({ success: true, result: result });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// AI等待
app.post('/api/wait', async (req, res) => {
    try {
        const { device_id, condition, timeout = 10 } = req.body;
        const agent = agents[device_id];
        
        if (!agent) {
            return res.json({ success: false, error: '设备代理未初始化' });
        }
        
        await agent.aiWaitFor(condition, { timeout: timeout * 1000 });
        
        res.json({ success: true, message: `条件满足: ${condition}` });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// AI断言
app.post('/api/assert', async (req, res) => {
    try {
        const { device_id, assertion } = req.body;
        const agent = agents[device_id];
        
        if (!agent) {
            return res.json({ success: false, error: '设备代理未初始化' });
        }
        
        await agent.aiAssert(assertion);
        
        res.json({ success: true, message: `断言成功: ${assertion}` });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// 获取页面信息
app.post('/api/page-info', async (req, res) => {
    try {
        const { device_id } = req.body;
        const page = devices[device_id];
        const agent = agents[device_id];
        
        if (!page || !agent) {
            return res.json({ success: false, error: '设备未连接' });
        }
        
        // 获取截图
        const screenshot = await page.screenshot();
        
        // 获取页面元素
        const elements = await agent.aiQuery(
            "List all interactive elements with their type, text, and location"
        );
        
        res.json({ 
            success: true,
            screenshot: screenshot,
            elements: elements || []
        });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// 关闭应用
app.post('/api/close', async (req, res) => {
    try {
        const { device_id, package_name } = req.body;
        const page = devices[device_id];
        
        if (!page) {
            return res.json({ success: false, error: '设备未连接' });
        }
        
        await page.closeApp(package_name);
        
        res.json({ success: true, message: `应用已关闭: ${package_name}` });
    } catch (error) {
        res.json({ success: false, error: error.message });
    }
});

// 健康检查
app.get('/api/health', (req, res) => {
    res.json({ success: true, message: 'API服务运行正常' });
});

app.listen(PORT, () => {
    console.log(`Midscene API服务器运行在端口 ${PORT}`);
});
'''
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(server_code)
    
    def _start_api_service(self):
        """启动API服务"""
        try:
            print(f"🚀 启动Midscene API服务 (端口: {self.api_port})...")
            
            self.api_process = subprocess.Popen(
                ["node", "server.js"],
                cwd=self.node_script_dir,
                env={**os.environ, "PORT": str(self.api_port)},
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待服务启动
            time.sleep(3)
            
            # 检查服务是否正常运行
            try:
                response = requests.get(f"{self.api_url}/api/health", timeout=5)
                if response.status_code == 200:
                    print("✅ Midscene API服务启动成功")
                else:
                    raise Exception("API服务健康检查失败")
            except requests.RequestException:
                raise Exception("无法连接到API服务")
                
        except Exception as e:
            if self.api_process:
                self.api_process.terminate()
            raise Exception(f"API服务启动失败: {str(e)}")
    
    def _verify_device_connection(self):
        """验证设备连接"""
        try:
            # 获取设备列表
            response = requests.get(f"{self.api_url}/api/devices", timeout=10)
            result = response.json()
            
            if not result.get("success"):
                raise Exception(result.get("error", "获取设备列表失败"))
            
            devices = result.get("devices", [])
            if not devices:
                raise Exception("没有找到连接的Android设备")
            
            if self.device_id:
                device_found = any(d.get("udid") == self.device_id for d in devices)
                if not device_found:
                    raise Exception(f"未找到指定设备: {self.device_id}")
            else:
                self.device_id = devices[0].get("udid")
            
            # 连接设备
            response = requests.post(f"{self.api_url}/api/connect", json={
                "device_id": self.device_id
            }, timeout=10)
            
            result = response.json()
            if not result.get("success"):
                raise Exception(result.get("error", "设备连接失败"))
            
            print(f"✅ 连接到设备: {self.device_id}")
            
        except Exception as e:
            raise Exception(f"设备连接验证失败: {str(e)}")
    
    def _api_call(self, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用API"""
        try:
            if data is None:
                data = {}
            
            data["device_id"] = self.device_id
            
            response = requests.post(f"{self.api_url}/api/{endpoint}", json=data, timeout=30)
            return response.json()
            
        except requests.RequestException as e:
            return {"success": False, "error": f"API调用失败: {str(e)}"}
    
    def install_and_launch_app(self, apk_path: str) -> ActionResult:
        """安装并启动应用"""
        try:
            print(f"📱 正在启动应用: {apk_path}")

            # 简化实现：直接启动（假设已安装）
            package_name = self._extract_package_name(apk_path)

            result = self._api_call("launch", {
                "package_name": package_name
            })

            if result.get("success"):
                self.current_package = package_name
                page_info = self.get_current_page_info()

                return ActionResult(
                    success=True,
                    message=result.get("message", "应用启动成功"),
                    new_page_info=page_info
                )
            else:
                return ActionResult(
                    success=False,
                    message=f"应用启动失败: {result.get('error')}",
                    error_details=result.get('error')
                )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"启动应用失败: {str(e)}",
                error_details=str(e)
            )

    def _extract_package_name(self, apk_path: str) -> str:
        """从APK路径提取包名"""
        filename = Path(apk_path).stem
        return f"com.example.{filename.lower()}"

    def get_current_page_info(self) -> PageInfo:
        """获取当前页面信息"""
        try:
            result = self._api_call("page-info")

            if result.get("success"):
                screenshot_data = result.get("screenshot", {})
                screenshot = ScreenshotResult(
                    image_path=screenshot_data.get("path", ""),
                    image_base64=screenshot_data.get("base64", ""),
                    width=screenshot_data.get("width", 1080),
                    height=screenshot_data.get("height", 1920),
                    timestamp=datetime.now().isoformat()
                )

                elements_data = result.get("elements", [])
                elements = self._parse_elements_from_ai(elements_data)

                return PageInfo(
                    screenshot=screenshot,
                    elements=elements,
                    activity_name=None,
                    package_name=self.current_package
                )
            else:
                raise Exception(result.get("error", "获取页面信息失败"))

        except Exception as e:
            print(f"获取页面信息失败: {str(e)}")
            return PageInfo(
                screenshot=ScreenshotResult(
                    image_path="",
                    image_base64="",
                    width=0,
                    height=0,
                    timestamp=datetime.now().isoformat()
                ),
                elements=[],
                activity_name=None,
                package_name=self.current_package
            )

    def _parse_elements_from_ai(self, elements_data: List[Dict]) -> List[UIElement]:
        """解析AI返回的元素数据"""
        elements = []

        for elem_data in elements_data:
            if isinstance(elem_data, dict):
                element = UIElement(
                    type=elem_data.get("type", "unknown").lower(),
                    text=elem_data.get("text"),
                    hint=elem_data.get("description"),
                    bounds=elem_data.get("bounds"),
                    clickable=elem_data.get("clickable", True),
                    scrollable=elem_data.get("scrollable", False),
                    attributes=elem_data.get("attributes", {})
                )
                elements.append(element)

        return elements

    def execute_action(self, action_description: str) -> ActionResult:
        """根据自然语言描述执行操作"""
        try:
            print(f"🎯 执行操作: {action_description}")

            result = self._api_call("action", {
                "action_description": action_description
            })

            if result.get("success"):
                new_page_info = self.get_current_page_info()

                return ActionResult(
                    success=True,
                    message=result.get("message", f"成功执行: {action_description}"),
                    new_page_info=new_page_info
                )
            else:
                return ActionResult(
                    success=False,
                    message=f"操作执行失败: {result.get('error')}",
                    error_details=result.get('error')
                )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"执行操作失败: {str(e)}",
                error_details=str(e)
            )

    def click_element(self, x: int, y: int) -> ActionResult:
        """点击指定坐标"""
        return self.execute_action(f"点击坐标 ({x}, {y})")

    def input_text(self, text: str, element_bounds: Optional[List[int]] = None) -> ActionResult:
        """输入文本"""
        if element_bounds:
            x1, y1, x2, y2 = element_bounds
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            action = f"在坐标 ({center_x}, {center_y}) 的输入框中输入 '{text}'"
        else:
            action = f"在当前输入框中输入 '{text}'"

        return self.execute_action(action)

    def scroll(self, direction: str = "down", distance: int = 500) -> ActionResult:
        """滚动页面"""
        direction_map = {
            "down": "向下滚动",
            "up": "向上滚动",
            "left": "向左滚动",
            "right": "向右滚动"
        }

        action = direction_map.get(direction, "向下滚动")
        return self.execute_action(action)

    def go_back(self) -> ActionResult:
        """返回上一页"""
        return self.execute_action("返回上一页")

    def close_app(self) -> ActionResult:
        """关闭应用"""
        try:
            result = self._api_call("close", {
                "package_name": self.current_package
            })

            if result.get("success"):
                return ActionResult(
                    success=True,
                    message=f"成功关闭应用: {self.current_package}"
                )
            else:
                return ActionResult(
                    success=False,
                    message=f"关闭应用失败: {result.get('error')}",
                    error_details=result.get('error')
                )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"关闭应用失败: {str(e)}",
                error_details=str(e)
            )

    def is_app_running(self) -> bool:
        """检查应用是否正在运行"""
        try:
            # 简化实现
            return self.current_package is not None
        except Exception:
            return False

    def ai_query(self, query: str) -> Any:
        """使用AI查询页面信息"""
        try:
            result = self._api_call("query", {"query": query})

            if result.get("success"):
                return result.get("result")
            else:
                return None

        except Exception as e:
            print(f"AI查询失败: {str(e)}")
            return None

    def ai_wait_for(self, condition: str, timeout: int = 10) -> bool:
        """等待指定条件"""
        try:
            result = self._api_call("wait", {
                "condition": condition,
                "timeout": timeout
            })

            return result.get("success", False)

        except Exception as e:
            print(f"AI等待失败: {str(e)}")
            return False

    def ai_assert(self, assertion: str) -> bool:
        """AI断言"""
        try:
            result = self._api_call("assert", {"assertion": assertion})

            return result.get("success", False)

        except Exception as e:
            print(f"AI断言失败: {str(e)}")
            return False

    def __del__(self):
        """析构函数，清理API服务进程"""
        if self.api_process:
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=5)
            except:
                try:
                    self.api_process.kill()
                except:
                    pass
