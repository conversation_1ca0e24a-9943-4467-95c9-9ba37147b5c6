"""
探索报告生成器 - 生成详细的应用探索报告
"""
import json
from typing import Dict, Any, List
from datetime import datetime
from pathlib import Path

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

from exploration_state import ExplorationState, get_exploration_summary


class ExplorationReportGenerator:
    """探索报告生成器"""
    
    def __init__(self, llm_model: str = "gpt-4"):
        self.llm = ChatOpenAI(model=llm_model, temperature=0.3)
    
    def generate_comprehensive_report(
        self,
        exploration_result: Dict[str, Any],
        output_format: str = "markdown"
    ) -> str:
        """生成综合探索报告"""
        
        if not exploration_result.get("success"):
            return self._generate_error_report(exploration_result)
        
        report_data = exploration_result.get("exploration_report", {})
        
        if output_format.lower() == "markdown":
            return self._generate_markdown_report(report_data, exploration_result)
        elif output_format.lower() == "html":
            return self._generate_html_report(report_data, exploration_result)
        elif output_format.lower() == "json":
            return self._generate_json_report(report_data, exploration_result)
        else:
            raise ValueError(f"不支持的输出格式: {output_format}")
    
    def _generate_markdown_report(self, report_data: Dict[str, Any], exploration_result: Dict[str, Any]) -> str:
        """生成Markdown格式报告"""
        
        summary = report_data.get("summary", {})
        page_stats = report_data.get("page_statistics", {})
        action_stats = report_data.get("action_statistics", {})
        exploration_path = report_data.get("exploration_path", [])
        goal_achievement = report_data.get("goal_achievement", {})
        
        report_lines = [
            "# 应用探索报告",
            "",
            f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**会话ID**: {exploration_result.get('session_id', 'N/A')}",
            "",
            "## 📊 探索概览",
            "",
            f"- **探索目标**: {goal_achievement.get('target_goal', 'N/A')}",
            f"- **总探索步数**: {summary.get('total_steps', 0)}",
            f"- **发现页面数**: {summary.get('unique_pages_visited', 0)}",
            f"- **成功操作数**: {summary.get('successful_actions', 0)}",
            f"- **成功率**: {summary.get('success_rate', 0):.1%}",
            f"- **探索时长**: {self._calculate_duration(summary)}",
            f"- **终止原因**: {summary.get('termination_reason', 'N/A')}",
            "",
            "## 🎯 目标达成情况",
            "",
        ]
        
        # 目标达成情况
        completed_goals = goal_achievement.get('completed_goals', [])
        if completed_goals:
            report_lines.append("### ✅ 已完成目标")
            for goal in completed_goals:
                report_lines.append(f"- {goal}")
            report_lines.append("")
        else:
            report_lines.append("### ⚠️ 未明确完成特定目标")
            report_lines.append("")
        
        # 页面发现统计
        report_lines.extend([
            "## 📱 页面发现统计",
            "",
            f"- **总发现页面**: {page_stats.get('total_pages_discovered', 0)}",
            ""
        ])
        
        # 页面相似度分析
        pages_by_similarity = page_stats.get('pages_by_similarity', {})
        if pages_by_similarity:
            report_lines.extend([
                "### 页面相似度分析",
                "",
                "| 页面ID | 与目标相似度 |",
                "|--------|-------------|"
            ])
            for page_id, similarity in pages_by_similarity.items():
                report_lines.append(f"| {page_id} | {similarity:.3f} |")
            report_lines.append("")
        
        # 操作统计
        report_lines.extend([
            "## ⚡ 操作执行统计",
            "",
            f"- **总操作数**: {action_stats.get('total_actions', 0)}",
            f"- **成功操作**: {action_stats.get('successful_actions', 0)}",
            f"- **失败操作**: {action_stats.get('failed_actions', 0)}",
            ""
        ])
        
        # 探索路径
        if exploration_path:
            report_lines.extend([
                "## 🗺️ 探索路径详情",
                "",
                "| 步骤 | 操作描述 | 页面ID | 执行结果 |",
                "|------|----------|--------|----------|"
            ])
            
            for step in exploration_path:
                success_icon = "✅" if step.get('success') else "❌" if step.get('success') is False else "⏳"
                report_lines.append(
                    f"| {step.get('step', 'N/A')} | {step.get('action', 'N/A')} | "
                    f"{step.get('page', 'N/A')} | {success_icon} |"
                )
            report_lines.append("")
        
        # 智能分析和建议
        analysis = self._generate_intelligent_analysis(report_data, exploration_result)
        if analysis:
            report_lines.extend([
                "## 🤖 智能分析与建议",
                "",
                analysis,
                ""
            ])
        
        # 技术细节
        report_lines.extend([
            "## 🔧 技术细节",
            "",
            f"- **使用的LLM模型**: {self.llm.model_name}",
            f"- **页面管理**: ChromaDB向量数据库",
            f"- **工作流引擎**: LangGraph",
            f"- **最大重试次数**: {exploration_result.get('max_retries', 'N/A')}",
            ""
        ])
        
        return "\n".join(report_lines)
    
    def _generate_html_report(self, report_data: Dict[str, Any], exploration_result: Dict[str, Any]) -> str:
        """生成HTML格式报告"""
        # 简化实现，将Markdown转换为HTML
        markdown_report = self._generate_markdown_report(report_data, exploration_result)
        
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用探索报告</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
               line-height: 1.6; margin: 40px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; 
                     padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; margin-top: 30px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        th {{ background-color: #f8f9fa; font-weight: 600; }}
        .success {{ color: #27ae60; }}
        .error {{ color: #e74c3c; }}
        .warning {{ color: #f39c12; }}
        .metric {{ background: #ecf0f1; padding: 15px; border-radius: 5px; margin: 10px 0; }}
    </style>
</head>
<body>
    <div class="container">
        <pre>{markdown_report}</pre>
    </div>
</body>
</html>
"""
        return html_template
    
    def _generate_json_report(self, report_data: Dict[str, Any], exploration_result: Dict[str, Any]) -> str:
        """生成JSON格式报告"""
        full_report = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "session_id": exploration_result.get("session_id"),
                "generator_version": "1.0.0"
            },
            "exploration_result": exploration_result,
            "detailed_report": report_data
        }
        
        return json.dumps(full_report, ensure_ascii=False, indent=2)
    
    def _generate_error_report(self, exploration_result: Dict[str, Any]) -> str:
        """生成错误报告"""
        return f"""
# 应用探索报告 - 执行失败

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## ❌ 执行失败

**错误信息**: {exploration_result.get('error', '未知错误')}

## 建议

1. 检查APK文件路径是否正确
2. 确认Executor实现是否正常工作
3. 检查网络连接和LLM API配置
4. 查看详细错误日志进行调试
"""
    
    def _calculate_duration(self, summary: Dict[str, Any]) -> str:
        """计算探索时长"""
        try:
            start_time = datetime.fromisoformat(summary.get('start_time', ''))
            end_time = datetime.fromisoformat(summary.get('end_time', ''))
            duration = end_time - start_time
            
            total_seconds = int(duration.total_seconds())
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            
            return f"{minutes}分{seconds}秒"
        except:
            return "未知"
    
    def _generate_intelligent_analysis(self, report_data: Dict[str, Any], exploration_result: Dict[str, Any]) -> str:
        """使用LLM生成智能分析"""
        try:
            summary = report_data.get("summary", {})
            goal_achievement = report_data.get("goal_achievement", {})
            
            prompt = f"""
基于以下应用探索数据，请提供专业的分析和建议：

探索目标: {goal_achievement.get('target_goal', 'N/A')}
总步数: {summary.get('total_steps', 0)}
成功率: {summary.get('success_rate', 0):.1%}
发现页面: {summary.get('unique_pages_visited', 0)}
终止原因: {summary.get('termination_reason', 'N/A')}

请从以下角度进行分析：
1. 探索效率评估
2. 目标达成度分析
3. 应用UI/UX特点
4. 改进建议

请用简洁专业的语言回答，重点突出关键发现和实用建议。
"""
            
            messages = [
                SystemMessage(content="你是一个专业的移动应用测试和用户体验分析专家。"),
                HumanMessage(content=prompt)
            ]
            
            response = self.llm.invoke(messages)
            return response.content.strip()
            
        except Exception as e:
            return f"智能分析生成失败: {str(e)}"
    
    def save_report_to_file(self, report_content: str, file_path: str, format_type: str = "markdown"):
        """保存报告到文件"""
        try:
            path = Path(file_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"📄 报告已保存到: {file_path}")
            
        except Exception as e:
            print(f"❌ 保存报告失败: {str(e)}")


def generate_exploration_report(
    exploration_result: Dict[str, Any],
    output_path: str = None,
    format_type: str = "markdown",
    llm_model: str = "gpt-4"
) -> str:
    """生成探索报告的便捷函数"""
    
    generator = ExplorationReportGenerator(llm_model)
    report_content = generator.generate_comprehensive_report(exploration_result, format_type)
    
    if output_path:
        generator.save_report_to_file(report_content, output_path, format_type)
    
    return report_content


# 示例使用
if __name__ == "__main__":
    # 模拟探索结果
    mock_result = {
        "success": True,
        "session_id": "exploration_20241201_143022",
        "total_steps": 8,
        "pages_discovered": 4,
        "termination_reason": "已完成探索目标",
        "exploration_report": {
            "summary": {
                "session_id": "exploration_20241201_143022",
                "start_time": "2024-12-01T14:30:22",
                "end_time": "2024-12-01T14:35:45",
                "total_steps": 8,
                "unique_pages_visited": 4,
                "successful_actions": 7,
                "success_rate": 0.875,
                "exploration_goal": "探索应用的登录和主要功能",
                "completed_goals": ["成功登录", "访问主页"],
                "termination_reason": "已完成探索目标"
            },
            "goal_achievement": {
                "target_goal": "探索应用的登录和主要功能，找到用户设置页面",
                "completed_goals": ["成功登录", "访问主页"],
                "termination_reason": "已完成探索目标"
            }
        }
    }
    
    # 生成报告
    report = generate_exploration_report(
        mock_result,
        output_path="./reports/exploration_report.md",
        format_type="markdown"
    )
    
    print("报告预览:")
    print(report[:500] + "...")
