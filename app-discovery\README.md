# App Discovery - 页面节点管理系统

基于ChromaDB的应用页面跳转关系存储和检索系统。

## 功能特性

- **页面节点存储**: 使用ChromaDB存储页面信息，包括截图、UI元素、语义描述等
- **向量化搜索**: 基于语义相似度的页面搜索功能
- **页面关系管理**: 支持父子页面关系和导航路径追踪
- **高性能检索**: 利用ChromaDB的向量数据库优势，实现快速相似性搜索

## 核心组件

### 1. PageTreeNode (models.py)
页面树节点类，包含：
- 页面ID和状态数据
- UI元素信息（按钮、输入框等）
- 语义描述和向量表示
- 父子关系管理

### 2. PageNodeManager (page_manager.py)
页面节点管理器，提供：
- 添加页面节点到ChromaDB
- 基于语义相似度的页面搜索
- 页面关系查询（父子节点、导航路径）
- 存储统计信息

## 使用示例

### 基础用法

```python
from models import PageTreeNode
from page_manager import PageNodeManager

# 创建管理器
manager = PageNodeManager()

# 创建页面节点
page = PageTreeNode("login_001", {
    "screenshot": "screenshots/login.png",
    "elements": [
        {"type": "input", "hint": "用户名", "bounds": [100, 200, 300, 240]},
        {"type": "button", "text": "登录", "bounds": [150, 320, 250, 360]}
    ],
    "semantic_desc": "登录页面，包含用户名和密码输入框",
    "depth": 0,
    "is_goal": False
})

# 添加到数据库
manager.add_page_node(page)

# 搜索相似页面
results = manager.find_similar_pages("用户登录", top_k=5)
for page, similarity in results:
    print(f"{page.id}: {similarity:.3f}")
```

### 高级功能

```python
# 获取子页面
children = manager.get_children("home_001")

# 获取导航路径
path = manager.get_page_path("target_page_001")

# 获取统计信息
stats = manager.get_stats()
```

## 运行演示

### 基础演示
```bash
uv run python main.py
```

### 高级演示（复杂应用结构）
```bash
uv run python demo_advanced.py
```

## 依赖项

- ChromaDB: 向量数据库
- NumPy: 数值计算
- scikit-learn: 机器学习工具

## 安装

```bash
# 基础依赖
uv add chromadb numpy scikit-learn

# LangGraph和LLM依赖
uv add langgraph langchain langchain-openai
```

## LangGraph自动化探索使用方法

### 快速开始

```python
from app_explorer import AppExplorer
from executor import MockExecutor  # 或你的实际Executor实现

# 创建探索器
executor = MockExecutor()  # 替换为实际的Executor
explorer = AppExplorer(executor=executor, llm_model="gpt-4")

# 运行探索
result = explorer.explore_app(
    apk_path="/path/to/your/app.apk",
    goal_description="探索应用的登录流程和主要功能",
    target_features=["用户登录", "主页导航", "设置页面"],
    completion_criteria=["成功登录", "访问主页", "找到设置"],
    generate_report=True
)

print(f"探索成功: {result['success']}")
print(f"报告路径: {result.get('report_path')}")
```

### 自定义Executor实现

```python
from executor import ExecutorInterface, ActionResult, PageInfo

class YourCustomExecutor(ExecutorInterface):
    def install_and_launch_app(self, apk_path: str) -> ActionResult:
        # 实现APK安装和启动逻辑
        pass

    def get_current_page_info(self) -> PageInfo:
        # 实现页面信息获取逻辑
        pass

    def execute_action(self, action_description: str) -> ActionResult:
        # 实现自然语言操作执行逻辑
        pass

    # 实现其他必需方法...
```

### 环境配置

```bash
# 设置OpenAI API密钥
export OPENAI_API_KEY="your-api-key-here"

# 或者使用其他LLM提供商
export ANTHROPIC_API_KEY="your-anthropic-key"
```

## 数据结构

### 页面节点数据格式
```python
{
    "id": "page_001",
    "state_data": {
        "screenshot": "path/to/screenshot.png",
        "elements": [
            {"type": "button", "text": "登录", "bounds": [x1,y1,x2,y2]},
            {"type": "input", "hint": "用户名", "bounds": [x1,y1,x2,y2]}
        ],
        "semantic_desc": "页面的语义描述",
        "depth": 0,
        "is_goal": False
    },
    "parent_id": "parent_page_id",
    "action_from_parent": "点击登录按钮",
    "children_ids": ["child1", "child2"]
}
```

## ChromaDB优势

1. **自动向量化**: 自动将文本转换为向量表示
2. **高效搜索**: 基于余弦相似度的快速检索
3. **持久化存储**: 数据自动持久化到磁盘
4. **可扩展性**: 支持大规模数据存储和查询
5. **易于使用**: 简单的API接口

## 应用场景

- 移动应用UI自动化测试
- 网页爬虫路径规划
- 应用页面关系分析
- 用户行为路径追踪
- 智能导航系统

## 🤖 LangGraph自动化应用探索系统

基于LangGraph实现的智能应用探索系统，能够根据用户指令自动化探索移动应用并生成详细报告。

### 核心特性

- **智能探索**: 使用大模型进行决策，自动规划探索路径
- **页面识别**: 自动识别和记录应用页面，避免重复探索
- **目标导向**: 根据用户设定的探索目标进行有针对性的探索
- **详细报告**: 自动生成包含探索路径、页面发现、操作统计的详细报告
- **可扩展架构**: 支持不同的Executor实现，适配各种自动化工具

### 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户指令      │───▶│  LangGraph工作流  │───▶│   探索报告      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Executor      │◀───│   探索节点       │───▶│  ChromaDB       │
│   (应用交互)     │    │   (LLM决策)      │    │  (页面存储)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 工作流程

1. **初始化**: Executor安装并启动目标APK
2. **页面分析**: 获取当前页面截图和UI元素，生成语义描述
3. **重复检测**: 检查页面是否已经访问过，避免重复探索
4. **智能决策**: LLM根据探索目标规划下一步操作
5. **操作执行**: Executor执行计划的操作
6. **循环探索**: 重复步骤2-5直到完成目标或无更多操作
7. **报告生成**: 生成详细的探索报告

## 新增组件

### LangGraph工作流系统

- **exploration_state.py**: 探索状态管理
- **exploration_nodes.py**: LangGraph节点定义
- **exploration_workflow.py**: 工作流编排
- **executor.py**: 应用交互接口定义
- **report_generator.py**: 智能报告生成
- **app_explorer.py**: 主要探索程序

## 项目结构

```
app-discovery/
├── models.py                 # 页面节点数据模型
├── page_manager.py           # ChromaDB页面管理器
├── executor.py               # Executor接口定义
├── exploration_state.py      # LangGraph状态管理
├── exploration_nodes.py      # 探索节点实现
├── exploration_workflow.py   # LangGraph工作流
├── report_generator.py       # 报告生成器
├── app_explorer.py          # 主要探索程序
├── main.py                  # 基础演示
├── demo_advanced.py         # 高级演示
├── simple_vectorizer.py     # 简单向量化器（备用）
├── README.md               # 项目文档
├── chroma_db/              # ChromaDB数据存储目录
├── chroma_exploration/     # 探索专用ChromaDB
└── reports/                # 生成的探索报告
```