"""
LangGraph节点定义 - 实现探索过程中的各个处理步骤
"""
import hashlib
from typing import Dict, Any, List, Tuple
from datetime import datetime

from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

from executor import ExecutorInterface, PageInfo
from exploration_state import ExplorationState, add_exploration_step, add_visited_page, set_termination, increment_retry, reset_retry
from models import PageTreeNode
from page_manager import PageNodeManager


class ExplorationNodes:
    """探索节点集合"""
    
    def __init__(self, executor: ExecutorInterface, page_manager: PageNodeManager, llm_model: str = "gpt-4"):
        self.executor = executor
        self.page_manager = page_manager
        self.llm = ChatOpenAI(model=llm_model, temperature=0.1)
    
    def initialize_app(self, state: ExplorationState) -> ExplorationState:
        """初始化应用 - 安装并启动APK"""
        print(f"🚀 初始化应用: {state['apk_path']}")
        
        try:
            # 调用executor安装并启动应用
            result = self.executor.install_and_launch_app(state["apk_path"])
            
            if result.success:
                print("✅ 应用启动成功")
                
                # 获取初始页面信息
                if result.new_page_info:
                    page_info = result.new_page_info
                else:
                    page_info = self.executor.get_current_page_info()
                
                # 生成页面ID
                page_id = self._generate_page_id(page_info)
                state["current_page_info"] = page_info
                state["current_page_id"] = page_id
                
                # 记录初始化步骤
                state = add_exploration_step(
                    state,
                    action_description="初始化应用",
                    action_result=result,
                    page_description="应用启动页面",
                    is_new_page=True
                )
                
                state = reset_retry(state)
                
            else:
                print(f"❌ 应用启动失败: {result.message}")
                state = increment_retry(state, f"应用启动失败: {result.message}")
                
        except Exception as e:
            error_msg = f"初始化应用时发生异常: {str(e)}"
            print(f"❌ {error_msg}")
            state = increment_retry(state, error_msg)
        
        return state
    
    def analyze_current_page(self, state: ExplorationState) -> ExplorationState:
        """分析当前页面 - 生成页面描述并检查是否为新页面"""
        print("🔍 分析当前页面...")
        
        if not state["current_page_info"]:
            print("⚠️ 没有当前页面信息")
            return state
        
        try:
            page_info = state["current_page_info"]
            page_id = state["current_page_id"]
            
            # 生成页面描述
            page_description = self._generate_page_description(page_info, state["exploration_goal"])
            print(f"📄 页面描述: {page_description}")
            
            # 检查是否为新页面
            is_new_page, similarity_score = self._check_if_new_page(page_info, page_description)
            
            if is_new_page:
                print("🆕 发现新页面，记录到页面树")
                
                # 创建页面节点
                page_node = self._create_page_node(page_id, page_info, page_description, state)
                
                # 添加到页面管理器
                self.page_manager.add_page_node(page_node)
                
                # 更新状态
                state = add_visited_page(state, page_id, page_node)
                state["page_similarities"][page_id] = similarity_score
                
            else:
                print(f"🔄 页面已存在，相似度: {similarity_score:.3f}")
            
            # 更新最后一个探索步骤的信息
            if state["exploration_steps"]:
                last_step = state["exploration_steps"][-1]
                last_step.page_description = page_description
                last_step.is_new_page = is_new_page
                last_step.similarity_score = similarity_score
            
            state = reset_retry(state)
            
        except Exception as e:
            error_msg = f"分析页面时发生异常: {str(e)}"
            print(f"❌ {error_msg}")
            state = increment_retry(state, error_msg)
        
        return state
    
    def plan_next_action(self, state: ExplorationState) -> ExplorationState:
        """规划下一步操作"""
        print("🤔 规划下一步操作...")
        
        try:
            # 构建提示信息
            context = self._build_planning_context(state)
            
            # 调用LLM进行决策
            action_plan = self._llm_plan_action(context, state["exploration_goal"])
            
            print(f"📋 计划的操作: {action_plan}")
            state["next_action_plan"] = action_plan
            
            # 检查是否已经完成探索目标
            if self._should_terminate_exploration(state, action_plan):
                state = set_termination(state, "已完成探索目标或无更多可执行操作")
                print("🏁 探索完成")
            
            state = reset_retry(state)
            
        except Exception as e:
            error_msg = f"规划操作时发生异常: {str(e)}"
            print(f"❌ {error_msg}")
            state = increment_retry(state, error_msg)
        
        return state
    
    def execute_action(self, state: ExplorationState) -> ExplorationState:
        """执行计划的操作"""
        action_plan = state["next_action_plan"]
        print(f"⚡ 执行操作: {action_plan}")
        
        try:
            # 检查是否已经执行过类似操作
            if self._is_action_already_executed(action_plan, state):
                print("⏭️ 类似操作已执行过，跳过")
                # 尝试生成替代操作
                alternative_action = self._generate_alternative_action(state)
                if alternative_action:
                    action_plan = alternative_action
                    state["next_action_plan"] = action_plan
                    print(f"🔄 尝试替代操作: {action_plan}")
                else:
                    state = set_termination(state, "无更多可执行的新操作")
                    return state
            
            # 执行操作
            result = self.executor.execute_action(action_plan)
            
            if result.success:
                print("✅ 操作执行成功")
                
                # 更新页面信息
                if result.new_page_info:
                    new_page_id = self._generate_page_id(result.new_page_info)
                    state["current_page_info"] = result.new_page_info
                    state["current_page_id"] = new_page_id
                
                # 记录执行步骤
                state = add_exploration_step(
                    state,
                    action_description=action_plan,
                    action_result=result
                )
                
                state = reset_retry(state)
                
            else:
                print(f"❌ 操作执行失败: {result.message}")
                state = increment_retry(state, f"操作执行失败: {result.message}")
                
        except Exception as e:
            error_msg = f"执行操作时发生异常: {str(e)}"
            print(f"❌ {error_msg}")
            state = increment_retry(state, error_msg)
        
        return state
    
    def generate_report(self, state: ExplorationState) -> ExplorationState:
        """生成探索报告"""
        print("📊 生成探索报告...")
        
        try:
            report = self._create_exploration_report(state)
            state["exploration_report"] = report
            
            print("✅ 探索报告生成完成")
            
        except Exception as e:
            error_msg = f"生成报告时发生异常: {str(e)}"
            print(f"❌ {error_msg}")
            state["exploration_report"] = {"error": error_msg}
        
        return state
    
    def _generate_page_id(self, page_info: PageInfo) -> str:
        """生成页面唯一ID"""
        # 基于页面内容生成哈希ID
        content = f"{page_info.activity_name}_{page_info.package_name}"
        if page_info.elements:
            element_texts = [elem.text or elem.hint or elem.type for elem in page_info.elements]
            content += "_" + "_".join(filter(None, element_texts))
        
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def _generate_page_description(self, page_info: PageInfo, goal) -> str:
        """使用LLM生成页面描述"""
        # 构建页面信息
        elements_info = []
        for elem in page_info.elements:
            elem_desc = f"{elem.type}"
            if elem.text:
                elem_desc += f": '{elem.text}'"
            elif elem.hint:
                elem_desc += f": '{elem.hint}'"
            if elem.clickable:
                elem_desc += " (可点击)"
            elements_info.append(elem_desc)
        
        prompt = f"""
请分析这个应用页面并生成简洁的描述。

探索目标: {goal.description}

页面信息:
- Activity: {page_info.activity_name}
- Package: {page_info.package_name}
- UI元素: {', '.join(elements_info)}

请用一句话描述这个页面的主要功能和内容，重点关注与探索目标相关的元素。
"""
        
        messages = [
            SystemMessage(content="你是一个专业的移动应用UI分析师，擅长简洁准确地描述应用页面。"),
            HumanMessage(content=prompt)
        ]
        
        response = self.llm.invoke(messages)
        return response.content.strip()
    
    def _check_if_new_page(self, page_info: PageInfo, description: str) -> Tuple[bool, float]:
        """检查是否为新页面"""
        # 使用页面管理器的相似性搜索
        similar_pages = self.page_manager.find_similar_pages(description, top_k=1)
        
        if similar_pages:
            _, similarity = similar_pages[0]
            # 如果相似度超过阈值，认为是相同页面
            is_new = similarity < 0.8
            return is_new, similarity
        else:
            return True, 0.0
    
    def _create_page_node(self, page_id: str, page_info: PageInfo, description: str, state: ExplorationState) -> PageTreeNode:
        """创建页面节点"""
        # 转换UI元素
        elements_data = []
        for elem in page_info.elements:
            elem_dict = {
                "type": elem.type,
                "text": elem.text,
                "hint": elem.hint,
                "bounds": elem.bounds,
                "clickable": elem.clickable,
                "scrollable": elem.scrollable
            }
            elements_data.append(elem_dict)
        
        state_data = {
            "screenshot": page_info.screenshot.image_path,
            "elements": elements_data,
            "semantic_desc": description,
            "depth": state["current_step"],
            "is_goal": False  # 可以根据目标匹配来设置
        }
        
        return PageTreeNode(page_id, state_data)
    
    def _build_planning_context(self, state: ExplorationState) -> str:
        """构建规划上下文"""
        context_parts = []
        
        # 探索目标
        context_parts.append(f"探索目标: {state['exploration_goal'].description}")
        
        # 当前页面信息
        if state["current_page_info"]:
            page_info = state["current_page_info"]
            elements = [f"{elem.type}({elem.text or elem.hint})" for elem in page_info.elements if elem.clickable]
            context_parts.append(f"当前页面可操作元素: {', '.join(elements)}")
        
        # 已访问页面
        visited_pages = list(state["visited_pages"].keys())
        context_parts.append(f"已访问页面: {', '.join(visited_pages)}")
        
        # 最近的操作历史
        recent_steps = state["exploration_steps"][-3:] if len(state["exploration_steps"]) > 3 else state["exploration_steps"]
        if recent_steps:
            history = [f"{step.step_id}. {step.action_description}" for step in recent_steps]
            context_parts.append(f"最近操作: {'; '.join(history)}")
        
        return "\n".join(context_parts)
    
    def _llm_plan_action(self, context: str, goal) -> str:
        """使用LLM规划下一步操作"""
        prompt = f"""
基于当前情况，请规划下一步最合适的操作来实现探索目标。

{context}

请用自然语言描述下一步要执行的具体操作，例如：
- "点击登录按钮"
- "输入用户名test123"
- "向下滚动查看更多内容"
- "点击设置菜单"

要求：
1. 操作要具体明确
2. 优先探索与目标相关的功能
3. 避免重复已执行的操作
4. 如果已完成目标，返回"完成探索"

下一步操作:
"""
        
        messages = [
            SystemMessage(content="你是一个专业的移动应用测试专家，擅长规划高效的应用探索路径。"),
            HumanMessage(content=prompt)
        ]
        
        response = self.llm.invoke(messages)
        return response.content.strip()
    
    def _should_terminate_exploration(self, state: ExplorationState, action_plan: str) -> bool:
        """判断是否应该终止探索"""
        # 检查是否明确表示完成
        if "完成探索" in action_plan or "exploration complete" in action_plan.lower():
            return True
        
        # 检查是否达到最大步数
        if state["current_step"] >= 20:  # 最大探索步数
            return True
        
        # 检查是否无新页面发现
        recent_new_pages = sum(1 for step in state["exploration_steps"][-5:] if step.is_new_page)
        if state["current_step"] > 10 and recent_new_pages == 0:
            return True
        
        return False
    
    def _is_action_already_executed(self, action: str, state: ExplorationState) -> bool:
        """检查是否已执行过类似操作"""
        # 简单的文本相似性检查
        for step in state["exploration_steps"]:
            if step.action_description and action.lower() in step.action_description.lower():
                return True
        return False
    
    def _generate_alternative_action(self, state: ExplorationState) -> str:
        """生成替代操作"""
        # 简单实现：建议返回或探索其他元素
        if state["current_page_info"] and state["current_page_info"].elements:
            unused_elements = [elem for elem in state["current_page_info"].elements 
                             if elem.clickable and elem.text and 
                             not any(elem.text in step.action_description for step in state["exploration_steps"])]
            
            if unused_elements:
                elem = unused_elements[0]
                return f"点击{elem.text}"
        
        return "返回上一页"
    
    def _create_exploration_report(self, state: ExplorationState) -> Dict[str, Any]:
        """创建详细的探索报告"""
        from exploration_state import get_exploration_summary
        
        summary = get_exploration_summary(state)
        
        # 页面发现统计
        page_stats = {
            "total_pages_discovered": len(state["visited_pages"]),
            "pages_by_similarity": {
                page_id: score for page_id, score in state["page_similarities"].items()
            }
        }
        
        # 操作统计
        action_stats = {
            "total_actions": len(state["exploration_steps"]),
            "successful_actions": sum(1 for step in state["exploration_steps"] 
                                    if step.action_result and step.action_result.success),
            "failed_actions": sum(1 for step in state["exploration_steps"] 
                                if step.action_result and not step.action_result.success)
        }
        
        # 探索路径
        exploration_path = [
            {
                "step": step.step_id,
                "action": step.action_description,
                "page": step.page_id,
                "success": step.action_result.success if step.action_result else None
            }
            for step in state["exploration_steps"]
        ]
        
        return {
            "summary": summary,
            "page_statistics": page_stats,
            "action_statistics": action_stats,
            "exploration_path": exploration_path,
            "goal_achievement": {
                "target_goal": state["exploration_goal"].description,
                "completed_goals": state["completed_goals"],
                "termination_reason": state["termination_reason"]
            }
        }
