"""
基于Midscene Android SDK的执行器实现
通过Node.js子进程调用TypeScript代码
"""
import subprocess
import json
import os
import time
import base64
from typing import Dict, Any, Optional, List
from datetime import datetime
from pathlib import Path

from .executor import ExecutorInterface, ActionResult, PageInfo, ScreenshotResult, UIElement


class MidsceneExecutor(ExecutorInterface):
    """基于Midscene Android SDK的执行器"""
    
    def __init__(self, device_id: str = None, node_script_dir: str = "./midscene_scripts"):
        """
        初始化Midscene执行器
        
        Args:
            device_id: Android设备ID
            node_script_dir: Node.js脚本目录
        """
        self.device_id = device_id
        self.node_script_dir = Path(node_script_dir)
        self.current_package = None
        self.screenshots_dir = "./screenshots"
        
        # 确保目录存在
        os.makedirs(self.screenshots_dir, exist_ok=True)
        self.node_script_dir.mkdir(exist_ok=True)
        
        # 初始化Node.js环境
        self._setup_node_environment()
        
        # 验证设备连接
        self._verify_device_connection()
    
    def _setup_node_environment(self):
        """设置Node.js环境和依赖"""
        package_json_path = self.node_script_dir / "package.json"
        
        if not package_json_path.exists():
            # 创建package.json
            package_json = {
                "name": "midscene-executor",
                "version": "1.0.0",
                "type": "module",
                "dependencies": {
                    "@midscene/android": "latest",
                    "dotenv": "latest"
                }
            }
            
            with open(package_json_path, 'w') as f:
                json.dump(package_json, f, indent=2)
            
            print("📦 安装Node.js依赖...")
            try:
                subprocess.run(
                    ["npm", "install"], 
                    cwd=self.node_script_dir, 
                    check=True,
                    capture_output=True
                )
                print("✅ Node.js依赖安装完成")
            except subprocess.CalledProcessError as e:
                raise Exception(f"Node.js依赖安装失败: {e.stderr.decode()}")
    
    def _verify_device_connection(self):
        """验证设备连接"""
        try:
            result = self._call_node_script("get_devices.js", {})
            devices = result.get("devices", [])
            
            if not devices:
                raise Exception("没有找到连接的Android设备")
            
            if self.device_id:
                device_found = any(d.get("udid") == self.device_id for d in devices)
                if not device_found:
                    raise Exception(f"未找到指定设备: {self.device_id}")
            else:
                # 使用第一个设备
                self.device_id = devices[0].get("udid")
            
            print(f"✅ 连接到设备: {self.device_id}")
            
        except Exception as e:
            raise Exception(f"设备连接验证失败: {str(e)}")
    
    def _call_node_script(self, script_name: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """调用Node.js脚本"""
        script_path = self.node_script_dir / script_name
        
        # 确保脚本存在
        if not script_path.exists():
            self._create_node_script(script_name)
        
        # 准备输入参数
        input_data = {
            "device_id": self.device_id,
            "params": params
        }
        
        try:
            # 调用Node.js脚本
            result = subprocess.run(
                ["node", str(script_path)],
                input=json.dumps(input_data),
                capture_output=True,
                text=True,
                cwd=self.node_script_dir,
                timeout=60
            )
            
            if result.returncode != 0:
                raise Exception(f"Node.js脚本执行失败: {result.stderr}")
            
            # 解析输出
            try:
                return json.loads(result.stdout)
            except json.JSONDecodeError:
                # 如果不是JSON，返回原始输出
                return {"output": result.stdout, "success": True}
                
        except subprocess.TimeoutExpired:
            raise Exception(f"Node.js脚本执行超时: {script_name}")
        except Exception as e:
            raise Exception(f"调用Node.js脚本失败: {str(e)}")
    
    def _create_node_script(self, script_name: str):
        """创建Node.js脚本"""
        script_path = self.node_script_dir / script_name
        
        if script_name == "get_devices.js":
            script_content = '''
import { getConnectedDevices } from '@midscene/android';
import process from 'process';

async function main() {
    try {
        const devices = await getConnectedDevices();
        console.log(JSON.stringify({ success: true, devices }));
    } catch (error) {
        console.log(JSON.stringify({ success: false, error: error.message }));
    }
}

main();
'''
        
        elif script_name == "install_and_launch.js":
            script_content = '''
import { AndroidAgent, AndroidDevice } from '@midscene/android';
import process from 'process';

async function main() {
    const input = JSON.parse(process.argv[2] || '{}');
    const { device_id, params } = input;
    
    try {
        const page = new AndroidDevice(device_id);
        await page.connect();
        
        if (params.apk_path) {
            // 安装APK (需要实现)
            // await page.installApp(params.apk_path);
        }
        
        if (params.package_name) {
            await page.launch(params.package_name);
        } else if (params.url) {
            await page.launch(params.url);
        }
        
        // 等待启动
        await new Promise(r => setTimeout(r, 3000));
        
        console.log(JSON.stringify({ 
            success: true, 
            message: "应用启动成功",
            package_name: params.package_name || params.url
        }));
        
    } catch (error) {
        console.log(JSON.stringify({ success: false, error: error.message }));
    }
}

// 从stdin读取参数
let inputData = '';
process.stdin.on('data', chunk => inputData += chunk);
process.stdin.on('end', () => {
    const input = JSON.parse(inputData);
    main(input).catch(console.error);
});
'''
        
        elif script_name == "execute_action.js":
            script_content = '''
import { AndroidAgent, AndroidDevice } from '@midscene/android';
import process from 'process';

async function main(input) {
    const { device_id, params } = input;
    
    try {
        const page = new AndroidDevice(device_id);
        const agent = new AndroidAgent(page, {
            aiActionContext: 'Perform the requested action naturally and efficiently.'
        });
        
        await page.connect();
        
        const result = await agent.aiAction(params.action_description);
        
        console.log(JSON.stringify({ 
            success: true, 
            message: `执行操作成功: ${params.action_description}`,
            result: result
        }));
        
    } catch (error) {
        console.log(JSON.stringify({ 
            success: false, 
            error: error.message,
            action: params.action_description
        }));
    }
}

// 从stdin读取参数
let inputData = '';
process.stdin.on('data', chunk => inputData += chunk);
process.stdin.on('end', () => {
    const input = JSON.parse(inputData);
    main(input).catch(console.error);
});
'''
        
        elif script_name == "close_app.js":
            script_content = '''
import { AndroidAgent, AndroidDevice } from '@midscene/android';
import process from 'process';

async function main(input) {
    const { device_id, params } = input;

    try {
        const page = new AndroidDevice(device_id);
        await page.connect();

        if (params.package_name) {
            await page.closeApp(params.package_name);
        }

        console.log(JSON.stringify({
            success: true,
            message: `应用已关闭: ${params.package_name}`
        }));

    } catch (error) {
        console.log(JSON.stringify({ success: false, error: error.message }));
    }
}

let inputData = '';
process.stdin.on('data', chunk => inputData += chunk);
process.stdin.on('end', () => {
    const input = JSON.parse(inputData);
    main(input).catch(console.error);
});
'''

        elif script_name == "ai_query.js":
            script_content = '''
import { AndroidAgent, AndroidDevice } from '@midscene/android';
import process from 'process';

async function main(input) {
    const { device_id, params } = input;

    try {
        const page = new AndroidDevice(device_id);
        const agent = new AndroidAgent(page);

        await page.connect();

        const result = await agent.aiQuery(params.query);

        console.log(JSON.stringify({
            success: true,
            result: result
        }));

    } catch (error) {
        console.log(JSON.stringify({ success: false, error: error.message }));
    }
}

let inputData = '';
process.stdin.on('data', chunk => inputData += chunk);
process.stdin.on('end', () => {
    const input = JSON.parse(inputData);
    main(input).catch(console.error);
});
'''

        elif script_name == "ai_wait_for.js":
            script_content = '''
import { AndroidAgent, AndroidDevice } from '@midscene/android';
import process from 'process';

async function main(input) {
    const { device_id, params } = input;

    try {
        const page = new AndroidDevice(device_id);
        const agent = new AndroidAgent(page);

        await page.connect();

        await agent.aiWaitFor(params.condition, { timeout: params.timeout * 1000 });

        console.log(JSON.stringify({
            success: true,
            message: `条件满足: ${params.condition}`
        }));

    } catch (error) {
        console.log(JSON.stringify({ success: false, error: error.message }));
    }
}

let inputData = '';
process.stdin.on('data', chunk => inputData += chunk);
process.stdin.on('end', () => {
    const input = JSON.parse(inputData);
    main(input).catch(console.error);
});
'''

        elif script_name == "ai_assert.js":
            script_content = '''
import { AndroidAgent, AndroidDevice } from '@midscene/android';
import process from 'process';

async function main(input) {
    const { device_id, params } = input;

    try {
        const page = new AndroidDevice(device_id);
        const agent = new AndroidAgent(page);

        await page.connect();

        await agent.aiAssert(params.assertion);

        console.log(JSON.stringify({
            success: true,
            message: `断言成功: ${params.assertion}`
        }));

    } catch (error) {
        console.log(JSON.stringify({ success: false, error: error.message }));
    }
}

let inputData = '';
process.stdin.on('data', chunk => inputData += chunk);
process.stdin.on('end', () => {
    const input = JSON.parse(inputData);
    main(input).catch(console.error);
});
'''

        elif script_name == "get_page_info.js":
            script_content = '''
import { AndroidAgent, AndroidDevice } from '@midscene/android';
import process from 'process';

async function main(input) {
    const { device_id, params } = input;
    
    try {
        const page = new AndroidDevice(device_id);
        const agent = new AndroidAgent(page);
        
        await page.connect();
        
        // 获取截图
        const screenshot = await page.screenshot();
        
        // 获取页面元素信息
        const elements = await agent.aiQuery(
            "List all interactive elements on the page with their type, text, and approximate location"
        );
        
        // 获取当前应用信息
        const currentApp = await page.getCurrentApp();
        
        console.log(JSON.stringify({ 
            success: true,
            screenshot: screenshot,
            elements: elements || [],
            current_app: currentApp
        }));
        
    } catch (error) {
        console.log(JSON.stringify({ success: false, error: error.message }));
    }
}

// 从stdin读取参数
let inputData = '';
process.stdin.on('data', chunk => inputData += chunk);
process.stdin.on('end', () => {
    const input = JSON.parse(inputData);
    main(input).catch(console.error);
});
'''
        
        else:
            raise ValueError(f"未知的脚本类型: {script_name}")
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
    
    def install_and_launch_app(self, apk_path: str) -> ActionResult:
        """安装并启动应用"""
        try:
            print(f"📱 正在安装并启动APK: {apk_path}")
            
            # 获取包名（简化实现）
            package_name = self._extract_package_name(apk_path)
            
            result = self._call_node_script("install_and_launch.js", {
                "apk_path": apk_path,
                "package_name": package_name
            })
            
            if result.get("success"):
                self.current_package = package_name
                
                # 获取页面信息
                page_info = self.get_current_page_info()
                
                return ActionResult(
                    success=True,
                    message=result.get("message", "应用启动成功"),
                    new_page_info=page_info
                )
            else:
                return ActionResult(
                    success=False,
                    message=f"应用启动失败: {result.get('error')}",
                    error_details=result.get('error')
                )
                
        except Exception as e:
            return ActionResult(
                success=False,
                message=f"安装启动应用失败: {str(e)}",
                error_details=str(e)
            )
    
    def _extract_package_name(self, apk_path: str) -> str:
        """从APK路径提取包名（简化实现）"""
        # 这里可以使用aapt工具或其他方法提取真实包名
        # 简化实现：从文件名推测
        filename = Path(apk_path).stem
        return f"com.example.{filename.lower()}"

    def get_current_page_info(self) -> PageInfo:
        """获取当前页面信息"""
        try:
            result = self._call_node_script("get_page_info.js", {})

            if result.get("success"):
                # 处理截图
                screenshot_data = result.get("screenshot", {})
                screenshot = ScreenshotResult(
                    image_path=screenshot_data.get("path", ""),
                    image_base64=screenshot_data.get("base64", ""),
                    width=screenshot_data.get("width", 1080),
                    height=screenshot_data.get("height", 1920),
                    timestamp=datetime.now().isoformat()
                )

                # 处理UI元素
                elements_data = result.get("elements", [])
                elements = self._parse_elements_from_ai(elements_data)

                # 处理应用信息
                current_app = result.get("current_app", {})

                return PageInfo(
                    screenshot=screenshot,
                    elements=elements,
                    activity_name=current_app.get("activity"),
                    package_name=current_app.get("package", self.current_package)
                )
            else:
                raise Exception(result.get("error", "获取页面信息失败"))

        except Exception as e:
            print(f"获取页面信息失败: {str(e)}")
            return PageInfo(
                screenshot=ScreenshotResult(
                    image_path="",
                    image_base64="",
                    width=0,
                    height=0,
                    timestamp=datetime.now().isoformat()
                ),
                elements=[],
                activity_name=None,
                package_name=self.current_package
            )

    def _parse_elements_from_ai(self, elements_data: List[Dict]) -> List[UIElement]:
        """解析AI返回的元素数据"""
        elements = []

        for elem_data in elements_data:
            if isinstance(elem_data, dict):
                element = UIElement(
                    type=elem_data.get("type", "unknown").lower(),
                    text=elem_data.get("text"),
                    hint=elem_data.get("description"),
                    bounds=elem_data.get("bounds"),
                    clickable=elem_data.get("clickable", True),
                    scrollable=elem_data.get("scrollable", False),
                    attributes=elem_data.get("attributes", {})
                )
                elements.append(element)

        return elements

    def execute_action(self, action_description: str) -> ActionResult:
        """根据自然语言描述执行操作"""
        try:
            print(f"🎯 执行操作: {action_description}")

            result = self._call_node_script("execute_action.js", {
                "action_description": action_description
            })

            if result.get("success"):
                # 获取新的页面信息
                new_page_info = self.get_current_page_info()

                return ActionResult(
                    success=True,
                    message=result.get("message", f"成功执行: {action_description}"),
                    new_page_info=new_page_info
                )
            else:
                return ActionResult(
                    success=False,
                    message=f"操作执行失败: {result.get('error')}",
                    error_details=result.get('error')
                )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"执行操作失败: {str(e)}",
                error_details=str(e)
            )

    def click_element(self, x: int, y: int) -> ActionResult:
        """点击指定坐标"""
        return self.execute_action(f"点击坐标 ({x}, {y})")

    def input_text(self, text: str, element_bounds: Optional[List[int]] = None) -> ActionResult:
        """输入文本"""
        if element_bounds:
            x1, y1, x2, y2 = element_bounds
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            action = f"在坐标 ({center_x}, {center_y}) 的输入框中输入 '{text}'"
        else:
            action = f"在当前输入框中输入 '{text}'"

        return self.execute_action(action)

    def scroll(self, direction: str = "down", distance: int = 500) -> ActionResult:
        """滚动页面"""
        direction_map = {
            "down": "向下滚动",
            "up": "向上滚动",
            "left": "向左滚动",
            "right": "向右滚动"
        }

        action = direction_map.get(direction, "向下滚动")
        return self.execute_action(action)

    def go_back(self) -> ActionResult:
        """返回上一页"""
        return self.execute_action("返回上一页")

    def close_app(self) -> ActionResult:
        """关闭应用"""
        try:
            result = self._call_node_script("close_app.js", {
                "package_name": self.current_package
            })

            if result.get("success"):
                return ActionResult(
                    success=True,
                    message=f"成功关闭应用: {self.current_package}"
                )
            else:
                return ActionResult(
                    success=False,
                    message=f"关闭应用失败: {result.get('error')}",
                    error_details=result.get('error')
                )

        except Exception as e:
            return ActionResult(
                success=False,
                message=f"关闭应用失败: {str(e)}",
                error_details=str(e)
            )

    def is_app_running(self) -> bool:
        """检查应用是否正在运行"""
        try:
            result = self._call_node_script("is_app_running.js", {
                "package_name": self.current_package
            })

            return result.get("is_running", False)

        except Exception:
            return False

    def ai_query(self, query: str) -> Any:
        """使用AI查询页面信息"""
        try:
            result = self._call_node_script("ai_query.js", {
                "query": query
            })

            if result.get("success"):
                return result.get("result")
            else:
                return None

        except Exception as e:
            print(f"AI查询失败: {str(e)}")
            return None

    def ai_wait_for(self, condition: str, timeout: int = 10) -> bool:
        """等待指定条件"""
        try:
            result = self._call_node_script("ai_wait_for.js", {
                "condition": condition,
                "timeout": timeout
            })

            return result.get("success", False)

        except Exception as e:
            print(f"AI等待失败: {str(e)}")
            return False

    def ai_assert(self, assertion: str) -> bool:
        """AI断言"""
        try:
            result = self._call_node_script("ai_assert.js", {
                "assertion": assertion
            })

            return result.get("success", False)

        except Exception as e:
            print(f"AI断言失败: {str(e)}")
            return False
